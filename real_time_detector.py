import cv2
import time
import threading
import numpy as np
from PIL import Image, ImageDraw, ImageFont

from assist import AssistBasicToolkit

import assist
from event_bus import event_bus

class RealTimeDetector:
    def __init__(self):
        self.hwnd = 0

        # 初始化assist工具包
        self.assist: AssistBasicToolkit = None
        self.window_ops = None
        self.screenshot = None
        self.detection = None
        self.image_search = None

        self.is_running = False

        # 绘制窗口的标题
        self.draw_title = "AI Vision"

        # 订阅程序开始事件与结束事件
        event_bus.subscribe('on_executor_started', self._on_executor_started)
        event_bus.subscribe('on_executor_stoped', self._on_executor_stoped)

    @classmethod
    def created(cls, hwnd, assist: AssistBasicToolkit) -> 'RealTimeDetector':
        self = cls()
        self.hwnd = hwnd
        self.assist = assist
        self.register_assist_objects()
        return self

    def register_assist_objects(self):
        self.window_ops = self.assist.window_ops
        self.screenshot = self.assist.screenshot
        self.detection = self.assist.detection
        self.image_search = self.assist.image_search

    def _on_executor_started(self):
        '''开始事件回调'''

        if self.is_running:
            return

        self.is_running = True

        # 初始化绘制区域（创建窗口、移动窗口、加载字体等）
        self._create_draw_area()

        # 开始录制视频
        self.screenshot.start_recording(target_fps=60, output_path="output.avi")
        
        self.run()
        
    def _on_executor_stoped(self):
        '''结束事件回调'''
        if not self.is_running:
            return

        # 设置停止标志
        self.is_running = False

        self.screenshot.stop_recording()
        self.screenshot.close()
        self._release_draw_area()

    def _create_draw_area(self):
        '''
        初始化绘制区域
        '''
        # 获取屏幕分辨率
        screen_w, screen_h = self.window_ops.get_screen_size()
        # 获取窗口位置和尺寸
        left, top, right, bottom = self.window_ops.get_window_rect(self.hwnd)
        # 计算宽高
        width = right - left
        height = bottom - top

        # 加载中文字体（需要下载字体文件）
        self.font_path = "simhei.ttf"  # 需下载微软雅黑或宋体等中文字体
        self.font_size = 20
        try:
            self.font = ImageFont.truetype(self.font_path, self.font_size)
        except:
            raise Exception(f"无法加载中文字体文件：{self.font_path}")
        
        # 创建显示窗口
        cv2.namedWindow(self.draw_title, cv2.WINDOW_NORMAL)
        cv2.resizeWindow(self.draw_title, width, height)
        print(f"窗口大小: {width}x{height}")

        # 移动窗口到右下角
        right_x = screen_w - width - 20
        bottom_y = screen_h - height - 20
        cv2.moveWindow(self.draw_title, right_x, bottom_y)

    def _release_draw_area(self):
        '''
        释放绘制区域
        '''
        cv2.destroyAllWindows()    
        
    def draw_detections(self, frame, results):
        try:
            # 转换OpenCV BGR图像为PIL RGB格式（只需要转换一次）
            pil_img = Image.fromarray(cv2.cvtColor(frame, cv2.COLOR_BGR2RGB))
            draw = ImageDraw.Draw(pil_img)
            
            for box in results.boxes:
                # 解析检测结果
                x1, y1, x2, y2 = map(int, box.xyxy[0].cpu().numpy())
                conf = box.conf.item()
                cls_id = int(box.cls.item())
                
                # 绘制边界框
                color = (0, 255, 0)  # 绿色边框
                draw.rectangle([x1, y1, x2, y2], outline=color, width=2)
                
                # 绘制中文标签
                label = f"{results.names[cls_id]} {conf:.2f}"
                bbox = self.font.getbbox(label)
                text_width, text_height = bbox[2] - bbox[0], bbox[3] - bbox[1]
                
                # 智能调整标签位置
                label_y = y1 - text_height if y1 > text_height else y1
                
                draw.text(
                    (x1, label_y),
                    label,
                    font=self.font,
                    fill=color
                )

            # 转换回OpenCV格式（只转换一次）
            annotated = cv2.cvtColor(np.array(pil_img), cv2.COLOR_RGB2BGR)

            # 显示处理后的画面（已经是BGR格式，无需再转换）
            cv2.imshow(self.draw_title, annotated)

            return annotated
            
        except Exception as e:
            print(f"绘制检测结果时出错: {str(e)}")
            return frame  # 发生错误时返回原始帧    
        
    def _main_loop(self):
        '''
        程序主循环操作，包含异常处理和性能监控
        '''
        try:
            # 高性能截图
            frame = self.screenshot.get_frame()
            if frame is None or frame.size == 0:
                print("警告：获取图像帧失败")
                return

            try:
                # 目标检测
                results = self.detection.detect_objects(frame)
                if results is None:
                    print("警告：目标检测返回空结果")
                    return
                    
                # 绘制检测结果
                annotated = self.draw_detections(frame, results)
                if annotated is None:
                    print("警告：绘制检测结果失败")
                    return
                
                # 将标注后的帧写入视频
                try:
                    self.screenshot.write_frame(annotated)
                except Exception as e:
                    print(f"写入视频帧时出错: {str(e)}")
                    
            except Exception as e:
                print(f"处理图像帧时出错: {str(e)}")
                # 出错时至少显示原始帧
                cv2.imshow(self.draw_title, frame)
                
        except Exception as e:
            print(f"主循环执行出错: {str(e)}")
            # 避免死循环，添加小延时
            time.sleep(0.1)

    def run(self):
        '''程序主循环'''

        try:
            while self.is_running:
                self._main_loop()
                cv2.waitKey(1)
                time.sleep(0.01)
        except KeyboardInterrupt:
            print("程序已停止，用户主动操作")
            event_bus.publish('_on_executor_stoped')
        
# 使用示例
if __name__ == "__main__":
    assist = AssistBasicToolkit()
    # 注册并绑定窗口操作对象
    hwnd = assist.register_and_bind_window_objects("大话西游2经典版 $Revision")
    # 记录模型路径（如assist.detection需指定模型，可在此处传递）
    assist.load_input_detection_model(r"models\xy2\xy2.pt")

    # 创建实时检测器
    detector = RealTimeDetector.created(hwnd, assist)

    # 发布开始事件
    event_bus.publish('on_executor_started')

    '''
    当前的检测器存在问题，暂时不启用，需要重新开发

    主要原因：
    cv2.imshow和cv2.waitKey在多线程中使用时，会导致程序卡死，无法使用子线程的方式创建实时检测器
    解决方案：
    1. 使用单线程方式运行检测器，确保cv2.imshow和cv2.waitKey在主线程中执行
    2. 将检测器的主循环放在主线程中，避免多线程冲突
    
    '''


