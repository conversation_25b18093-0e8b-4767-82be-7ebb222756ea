



import os, sys
base_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.join(base_dir, "."))


import time
import yaml
import traceback

from state_events.engine import Engine
from state_events.characters import Characters
from state_events.taskers import Taskers
from assist import AssistBasicToolkit

from real_time_detector import RealTimeDetector

from event_bus import event_bus

import logging
logger = logging.getLogger(__name__)

class GameController:
    def __init__(self, win_title, child_cls_name):

        # 初始化assist工具包
        self.assist = AssistBasicToolkit()
        # 注册并绑定窗口操作对象
        self.hwnd = self.assist.register_and_bind_window_objects(win_title)
        print(f"hwnd: {self.hwnd}, win_title: {win_title}")
        # 记录模型路径（如assist.detection需指定模型，可在此处传递）
        self.assist.detection.load_model(r'models\xy2\xy2.pt')

        # 依赖注入方式初始化组件
        self.engine = Engine.created(self.hwnd, self.assist)
        self.characters = Characters.created(self.hwnd, child_cls_name, self.assist)
        self.taskers = Taskers.created(self.hwnd, self.assist)

        # 创建实时检测器 当前检测器存在问题，暂时不启用
        # self.detector = RealTimeDetector.created(self.hwnd, self.assist)

    def _load_tasker(self, taskers_path=r"taskers.yaml"):
        '''加载任务信息'''
        try:
            with open(taskers_path, "r", encoding="utf-8") as f:
                taskers = yaml.load(f, Loader=yaml.FullLoader)
            return taskers
        except FileNotFoundError:
            logger.error(f"任务配置文件不存在: {taskers_path}")
            return {'单人任务': {}, '组队任务': {}}  # 返回默认配置
        except yaml.YAMLError as e:
            logger.error(f"YAML格式错误: {e}")
            return {'单人任务': {}, '组队任务': {}}
        except Exception as e:
            logger.error(f"加载任务配置失败: {e}")
            return {'单人任务': {}, '组队任务': {}}
        
    def run(self):
        """运行游戏控制器"""
        logger.warning("GameController is already running.")
        event_bus.publish('on_executor_started')

        # 从UI管理器中获取任务列表，当前临时使用硬编码
        tasker_config = self._load_tasker()
        self.oneself_taskers = [name for name in tasker_config['单人任务'].values()]
        self.group_taskers = [name for name in tasker_config['组队任务'].values()]
        # self.oneself_taskers = ['帮派任务', '街坊任务', '师门任务', '五环任务']
        self.oneself_taskers = ['新手剧情']
        event_bus.publish('on_tasker_registered', self.oneself_taskers)

        # 发布引擎启动事件
        event_bus.publish('on_engine_started', characters=self.characters)

        # 等待所有角色的任务完成
        self.wait_for_completed(timeout=60 * 60 * 2)

    def wait_for_completed(self, timeout=None):
        """等待所有角色的任务完成"""
        start_time = time.time()
        while True:
            try:
                if self.characters.is_all_finished() or (timeout and time.time() - start_time > timeout):
                    event_bus.publish('on_executor_stoped')
                    break
                time.sleep(1)
            except KeyboardInterrupt:
                event_bus.publish('on_executor_stoped')
            except Exception as e:
                logger.error(f"Error in wait_for_completed: {e}")
                logger.error(traceback.format_exc())
                event_bus.publish('on_executor_stoped')

        print(f"[GameController] 主线程终止")


if __name__ == '__main__':
    gc = GameController('大话西游2经典版 $Revision：', 'Win32Window')
    # gc = GameController('txt - Notepad', 'NotepadTextBox')
    gc.run()
    print(f"任务停止")
    # print(f"child_hwnd: {gc.child_hwnd}")
    # print(f"characters: {gc.characters}")
    # print(f"oneself_taskers: {gc.oneself_taskers}")
    # print(f"group_taskers: {gc.group_taskers}")

    # for hwnd, character in gc.characters.for_loop_characters():
    #     print(f"character.hwnd: {character.hwnd}, character.name: {character.name}, character.taskers: {character.tasker}")

    '''
    hwnd = 39328908
    class_name = Sandbox:DefaultBox:DHXYJYMainFrame
    win_title = 大话西游2经典版 $Revision：1791139 - 游戏试玩 - ψ景利ψ（ID：465786009）

    child_hwnd = 76747622
    child_cls_name = Sandbox:DefaultBox:Win32Window
    child_win_title = 大话西游2经典版 $Revision: 1791139 - 游戏试玩 - ψ景利ψ（ID：465786009）
    '''

    # import re
    # win_title = "大话西游2经典版 $Revision：1791139 - 游戏试玩 - ψ景利ψ（ID：465786009）"

    # pattern = ".*?(\d+) - (.*?) - (.*?)（ID：(\d+)）"
    # print(re.match(pattern, win_title).groups())



