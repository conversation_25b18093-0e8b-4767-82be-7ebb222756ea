import traceback
from ..state import State
from event_bus import event_bus

from assist import AssistBasicToolkit

import logging
logger = logging.getLogger(__name__)


class Callback:
    '''
    可调用对象
    '''

    def __init__(self, callback, *args, **kwargs):
        self.args = args
        self.kwargs = kwargs
        self.callback = callback

    def __call__(self):
        return self.callback(*self.args, **self.kwargs)

class Tasker:
    '''
    任务执行器
    '''

    def __init__(self):
        self.hwnd = 0
        self.name = ''
        self.handler = ''
        self.status = State.PENDING
        self.reason = ''
        self.callback = None

    @classmethod
    def bind_tasker(cls, hwnd, name, task_handler) -> 'Tasker':
        '''
        角色绑定任务执行器
        '''
        tasker = cls()
        tasker.hwnd = hwnd
        tasker.name = name

        if not task_handler:
            # logger.error(f"TaskHandler {name} not found")
            tasker.status = State.FAILED
            tasker.reason = f"TaskHandler {name} not found"
            return tasker

        handler = task_handler.created(hwnd)
        tasker.set_tasker_callback(handler.start)
        tasker.handler = handler

        return tasker
    
    def register_assist_objects(self, parent, assist: AssistBasicToolkit):
        '''绑定主窗口和assist'''
        self.handler.register_assist_objects(parent, assist)

    def _on_tasker_executed(self):
        '''
        执行任务
        '''
        try:
            if not self.handler:
                print(f"[Tasker] handler未找到: {self.name}")
                return

            if not self.callback:
                print(f"[Tasker] callback未设置: {self.name}")
                return

            # 调度执行具体任务，得到返回结果
            result = self.callback()

            # 事件驱动：发布一个处理结果事件
            event_bus.publish('on_result_processed', tasker=self, result=result)

        except Exception as e:
            print(f"[Tasker] 任务执行异常: {e}")
            print(traceback.format_exc())
            event_bus.publish('on_task_processed', tasker=self, result={'error': str(e)})

    def set_tasker_callback(self, callback, *args, **kwargs):
        '''
        设置任务回调
        '''
        if not callback:
            self.callback = None
            return
        if not args:
            args = ()
        if not kwargs:
            kwargs = {}

        self.callback = Callback(callback, *args, **kwargs)

    def set_tasker_state(self, state):
        '''
        设置任务状态
        '''
        self.status = state

    def set_tasker_reason(self, reason):
        '''
        设置任务阻塞原因
        '''
        self.reason = reason

    @property
    def _callback(self):
        '''
        获取回调名称
        '''
        if not self.callback:
            return 'None'
        return self.callback.callback.__name__
    