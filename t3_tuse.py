import os
import cv2
import time
import numpy as np
from PIL import Image
from assist import AssistBasicToolkit

class Tuse:

    def __init__(self, assist: AssistBasicToolkit=None):
        self.assist = assist

    def find_color(self, image, color_rgb, similarity=0.8):
        """
        在指定区域内查找目标颜色，返回格式为[(相似度, (left, top), (right, bottom))]，坐标为屏幕坐标。
        :param region: (left, top, right, bottom)
        :param color_rgb: 目标颜色
        :param similarity: 颜色相似度
        :return: [(相似度, (left, top), (right, bottom))]
        """
        points = self.assist.image_search._find_color(
            image, 
            (0, 0, image.size[0], image.size[1]),  # 使用整个图片作为区域
            color_rgb.lstrip('#'),  # 去掉'#'前缀
            similarity
        )
        print(f"匹配点: {points}")
        

    def find_color_text_regions(self, image, region, color_rgb, similarity=0.8):
        """
        查找区域内目标颜色的文字区域，返回格式为[(相似度, (left, top), (right, bottom), 框选文本)]，坐标为屏幕坐标。
        会自动合并相邻的同色文字区域。
        :param region: (left, top, right, bottom)
        :param color_rgb: 目标颜色
        :param similarity: 颜色相似度
        :return: [(相似度, (left, top), (right, bottom), 框选文本)]
        """
        # 获取区域截图
        region_img = image or self.assist.capture_region(region)
        if region_img is None:
            return []
        
        region_img = region_img.crop(region)
                    
        # 获取每个字的位置信息
        char_positions = self.assist.ocr.find_char_positions(region_img)
        
        draw, debug_img = self.assist.created_draw(region_img)
        
        # 存储匹配的字符区域
        matched_chars = []
        
        # 检查每个字符的颜色
        for item in char_positions:
            x1, y1, x2, y2 = item.get('bbox', (0, 0, 0, 0))
            x1, y1, x2, y2 = map(int, [x1, y1, x2, y2])
            char = item.get('char', '')
            if not char:
                continue
            
            # 边界保护
            x1, y1 = max(0, x1), max(0, y1)
            x2, y2 = min(region_img.size[0], x2), min(region_img.size[1], y2)
            if x2 <= x1 or y2 <= y1:
                continue
                
            # 提取字符区域图像
            char_region = region_img.crop((x1, y1, x2, y2))

            # char_region.save(os.path.join('temp', f"{char}_{int(time.time() * 1000)}.png"))  # 保存调试用
            
            # 处理字符区域（使用新的process_char_region方法）
            processed_region = self.assist.image_search.process_char_region(char_region)
            # processed_region.save(os.path.join('temp', f"{char}_{int(time.time() * 1000)}.png"))  # 保存调试用
            
            # 检查颜色匹配
            sim, pos = self.assist.image_search.find_color(
                processed_region, 
                (0, 0, x2-x1, y2-y1),
                color_rgb,
                similarity
            )
            # if char == '你':
            #     print(f"字符: {char}, 相似度: {sim:.2f}, 匹配位置: {pos}")
            #     points = self.assist.image_search._find_color(
            #         processed_region, 
            #         (0, 0, x2-x1, y2-y1), 
            #         color_rgb.lstrip('#'),  # 去掉'#'前缀
            #         similarity
            #     )
            #     print(f"匹配点: {points}")

            if not pos:  # 没有匹配到颜色
                continue
            # processed_region.save(os.path.join('temp', f"{char}_{int(time.time() * 1000)}.png"))  # 保存调试用

            if sim < similarity:  # 匹配度不达标
                continue
            
            matched_chars.append({
                'char': char,
                'sim': sim,
                'bbox': (x1, y1, x2, y2),
                'y': y1  # 用于后续行分组
            })
        
        # 如果没有找到任何匹配的字符
        if not matched_chars:
            self.assist.output_draw(debug_img, name='debug_color_regions', temp_dir='temp')
            return []
        
        # 合并相邻的字符区域
        merged_regions = self.assist.merge_char_regions(matched_chars)
        
        left, top, _, _ = region

        # 转换为屏幕坐标并生成结果
        result = []
        for region in merged_regions:
            x1, y1, x2, y2 = region['bbox']
            # 红色标记合并后的区域
            draw.rectangle([x1, y1, x2, y2], outline="red", width=2)
            
            # 转为客户区坐标
            cx1, cy1 = x1 + left, y1 + top
            cx2, cy2 = x2 + left, y2 + top
            result.append((region['sim'], (cx1, cy1), (cx2, cy2), region['text']))
        
        # 保存调试图片
        self.assist.output_draw(debug_img, name='debug_color_regions', temp_dir='temp')
        
        return sorted(result, key=lambda x: x[0], reverse=True)
    
    def find_ocr_by_color(self, image, target, region, color_rgb, similarity=0.8, fuzzy=False, use_regex=False, estimate_inner_bbox=True):
        """
        区域找字并按颜色精准筛选，返回('颜色相似度', (left, top), (right, bottom))，坐标为屏幕坐标。
        :param color_rgb: 目标颜色，支持以下格式：
            - RGB元组：(r,g,b)
            - 单点找色：'#RRGGBB'或'RRGGBB'
            - 多点找色：'RRGGBB|RRGGBB|...'（支持渐变色）
        :param similarity: 颜色相似度，范围0-1，默认0.8。设置建议：
            - 严格匹配：0.9以上
            - 一般匹配：0.7-0.9
            - 宽松匹配：0.5-0.7
        """
        # 获取区域截图
        region_img = image or self.assist.capture_region(region)
        if region_img is None:
            return 0, None, None, ''
        
        region_img = region_img.crop(region)
                        
        # 获取OCR结果
        results = self.assist.ocr.find_text_in_image(
            region_img, 
            target, 
            fuzzy=fuzzy, 
            use_regex=use_regex, 
            estimate_inner_bbox=estimate_inner_bbox
        )
        
        bests = []
        
        draw, debug_img = self.assist.created_draw(region_img)
        
        left, top, _, _ = region

        # 先用蓝色标记所有text_region
        for item in results:
            x1, y1, x2, y2 = item.get('bbox', (0, 0, 0, 0))
            x1, y1, x2, y2 = map(int, [x1, y1, x2, y2])
            text = item.get('text', '')
            
            # 边界保护
            x1, y1 = max(0, x1), max(0, y1)
            x2, y2 = min(region_img.size[0], x2), min(region_img.size[1], y2)
            if x2 <= x1 or y2 <= y1:
                continue
                
            # 蓝色标记所有识别到的文字区域
            draw.rectangle([x1, y1, x2, y2], outline="blue", width=2)
            
            # 提取文字区域图像
            text_region = region_img.crop((x1, y1, x2, y2))
            
            # 检查颜色匹配
            sim, pos = self.assist.image_search.find_color(
                text_region, 
                (0, 0, x2-x1, y2-y1),  # 相对于裁剪区域的坐标
                color_rgb,
                similarity
            )

            if not pos:  # 没有匹配到颜色
                continue
            if sim < similarity:  # 匹配度不达标
                continue
                
            # 红色标记颜色匹配成功的区域
            draw.rectangle([x1, y1, x2, y2], outline="red", width=2)
            
            # 转为客户区坐标
            cx1, cy1 = x1 + left, y1 + top
            cx2, cy2 = x2 + left, y2 + top
            bests.append((sim, (cx1, cy1), (cx2, cy2), text))
                
        # 保存调试图片
        self.assist.output_draw(debug_img, name='debug_color_ocr', temp_dir='temp')

        if not bests:
            return 0, None, None, ''
        return max(bests, key=lambda x: x[0])

    def find_detect_targets(self, image, target_name, region, conf_thres=0.3):
        """
        基于yolo模型在区域检测目标，传入目标名称和检测区域，返回(相似度, (left, top), (right, bottom))，坐标为屏幕坐标。
        :param target_name: 目标类别名（如'npc'）
        :param region: (left, top, right, bottom)
        :param conf_thres: 置信度阈值
        :return: (conf, (sx1, sy1), (sx2, sy2))
        """
        region_img = image or self.assist.capture_region(region)
        if region_img is None:
            return

        # 目标检测，返回单个Results对象
        results = self.assist.detection.detect_objects(region_img)

        found = []
        for box in results.boxes:
            cls_id = int(box.cls.item())
            conf = float(box.conf.item())

            # 获取类别名
            name = results.names[cls_id] if hasattr(results, 'names') else str(cls_id)
            if name != target_name:
                continue
            if conf < conf_thres:
                continue
            
            x1, y1, x2, y2 = map(int, box.xyxy[0].cpu().numpy())

            # 边界保护
            x1, y1 = max(0, x1), max(0, y1)
            x2, y2 = min(region_img.size[0], x2), min(region_img.size[1], y2)
            if x2 <= x1 or y2 <= y1:
                continue

            # 转为客户区坐标
            left, top, _, _ = region
            cx1, cy1 = x1 + left, y1 + top
            cx2, cy2 = x2 + left, y2 + top
            found.append((conf, (cx1, cy1), (cx2, cy2), name))

        if not found:
            return

        # 返回置信度最高的目标
        return max(found, key=lambda x: x[0])

if __name__ == "__main__":
    # win_title = '大话西游2经典版 $Revision：'
    # # 初始化assist工具包
    # assist = AssistBasicToolkit()
    # # 注册并绑定窗口操作对象
    # hwnd = assist.register_and_bind_window_objects(win_title)
    # print(f"hwnd: {hwnd}, win_title: {win_title}")

    # # 设置图片目录
    # image_dir = r'D:\Soft\Yolo\datasets\xy2\images'
    # image_name = '20250613185359.jpg'
    # image_path = os.path.join(image_dir, image_name)

    # # 加载图片
    # if not os.path.exists(image_path):
    #     raise FileNotFoundError(f"Image file not found: {image_path}")
    
    # image = cv2.imread(image_path)
    # image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))

    # # 创建Tuse实例
    # tuse = Tuse(assist)

    # 找任务名称
    # task_region = (14, 308, 295, 528)
    # ltps, rbps = (112, 350), (180, 371)
    # region = task_region[0], max(task_region[1], ltps[1]), task_region[2], task_region[3]
    # results = tuse.find_color_text_regions(
    #     image,
    #     region, 
    #     color_rgb='#ffff00', 
    #     similarity=0.8
    # )
    # for sim, (left, top), (right, bottom), text in results:
    #     print(f"相似度: {sim:.2f}, 区域: ({left}, {top}) - ({right}, {bottom}), 文本: {text}")

    # # 找交互内容
    # sim, ltps, rbps, text = tuse.find_ocr_by_color(
    #     image,
    #     '我把你弄出来', 
    #     (377, 517, 1179, 737), 
    #     color_rgb='#00ff00', 
    #     similarity=0.8
    # )
    # print(f"相似度: {sim:.2f}, 区域: ({ltps[0]}, {ltps[1]}) - ({rbps[0]}, {rbps[1]}), 文本: {text}")

    # # 找绿色文本内容
    # results = tuse.find_color_text_regions(
    #     image,
    #     (377, 517, 1179, 737), 
    #     color_rgb='#00ff00', 
    #     similarity=0.67
    # )
    # for sim, (left, top), (right, bottom), text in results:
    #     print(f"相似度: {sim:.2f}, 区域: ({left}, {top}) - ({right}, {bottom}), 文本: {text}")

    # image = cv2.imread(r'E:\xy2_yolo_auxiliary\temp\你_1750592149727.png')
    # image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
    # tuse.find_color(image, '#34ae37', similarity=0.8)

    x0, y0 = 15, 5
    w, h = 30, 20
    rects = []

    # i为列，j为行
    for i in range(6):
        for j in range(6):
            x1, y1 = j * w + x0, i * h + y0
            x2, y2 = (j + 1) * w + x0, (i + 1) * h + y0
            rect = (x1, y1, x2, y2)
            rects.append(rect)
            print(f"格子坐标区域: {rect}")

