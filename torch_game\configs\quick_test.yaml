# 快速测试配置文件

# 继承默认配置
inherit: "default.yaml"

# 环境配置
env:
  type: "Match3Env"
  board_size: 4       # 小棋盘，加快测试速度
  num_colors: 3       # 少颜色，简化游戏

# 智能体配置
agent:
  type: "PPOAgent"
  hidden_dims: [64, 32]  # 小网络，加快训练速度
  lr: 0.001             # 较大学习率，加快收敛
  gamma: 0.9            # 较小折扣因子，关注短期奖励

# 训练配置
training:
  num_episodes: 50      # 少量回合，快速测试
  max_steps: 50         # 较短回合
  buffer_capacity: 1000 # 小缓冲区
  batch_size: 32        # 小批次
  update_frequency: 2   # 频繁更新
  log_frequency: 5      # 频繁记录日志
  save_dir: "test_checkpoints"  # 测试保存目录

# 评估配置
evaluation:
  num_episodes: 5       # 少量评估回合
  render: true
  delay: 0.2            # 快速渲染

# 日志配置
logging:
  level: "DEBUG"        # 详细日志
  save_to_file: false   # 不保存到文件