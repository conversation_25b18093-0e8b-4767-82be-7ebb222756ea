class EventBus:
    """
    事件总线，支持事件的订阅、取消订阅和发布。
    用于模块间解耦通信。
    """
    def __init__(self):
        self.listeners = {}

    def subscribe(self, event: str, callback):
        """订阅事件"""
        self.listeners.setdefault(event, []).append(callback)

    def unsubscribe(self, event: str, callback):
        """取消订阅事件"""
        if event in self.listeners:
            self.listeners[event].remove(callback)
            if not self.listeners[event]:
                del self.listeners[event]

    def publish(self, event: str, *args, **kwargs):
        """发布事件，通知所有订阅者"""
        for cb in self.listeners.get(event, []):
            try:
                cb(*args, **kwargs)
            except Exception as e:
                print(f"[EventBus] 事件[{event}]回调异常: {e}")


# 单例实例
event_bus = EventBus()

# 日志与异常事件处理

def default_log_handler(level, message, **kwargs):
    print(f"[LOG][{level}] {message}")
    # 可扩展：写入文件、远程上报等

def default_error_handler(error, **kwargs):
    print(f"[ERROR] {error}")
    # 可扩展：写入文件、远程上报等

# 注册默认日志与异常事件订阅
event_bus.subscribe('on_log', default_log_handler)
event_bus.subscribe('on_error', default_error_handler) 