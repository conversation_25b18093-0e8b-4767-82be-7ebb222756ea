"""
修复版本：训练智能体玩真实点击式三消游戏的示例脚本
添加了完整的PPO训练逻辑和更好的调试信息
"""

import os
import sys
import argparse
import numpy as np
import torch
import time

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from torch_game.core.env import ClickMatch3Env
from torch_game.core.agents import PPOAgent

# 导入游戏助手（需要确保路径正确）
try:
    from s4_三消 import TripleEliminationGameAssistant
    from assist import AssistBasicToolkit
    GAME_ASSISTANT_AVAILABLE = True
except ImportError:
    print("警告: 无法导入游戏助手模块，将使用模拟模式")
    TripleEliminationGameAssistant = None
    AssistBasicToolkit = None
    GAME_ASSISTANT_AVAILABLE = False


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='Train an agent to play real click-style Match-3 game')
    
    # 环境参数
    parser.add_argument('--max_steps', type=int, default=50, help='Maximum steps per episode')
    parser.add_argument('--n_colors', type=int, default=6, help='Number of block colors')
    parser.add_argument('--storage_capacity', type=int, default=7, help='Storage area capacity')
    
    # 智能体参数
    parser.add_argument('--hidden_dims', type=int, nargs='+', default=[256, 128], help='Hidden dimensions')
    parser.add_argument('--lr', type=float, default=3e-4, help='Learning rate')
    parser.add_argument('--gamma', type=float, default=0.99, help='Discount factor')
    parser.add_argument('--clip_epsilon', type=float, default=0.2, help='PPO clip epsilon')
    
    # 训练参数
    parser.add_argument('--num_episodes', type=int, default=100, help='Number of training episodes')
    parser.add_argument('--log_frequency', type=int, default=10, help='Frequency of logging')
    parser.add_argument('--eval_frequency', type=int, default=50, help='Frequency of evaluation')
    
    # 游戏参数
    parser.add_argument('--game_window', type=str, default='最强祖师', help='Game window title')
    parser.add_argument('--model_path', type=str, default=r"models\sanxiao\best.pt", help='YOLO model path')
    parser.add_argument('--simulate_mode', action='store_true', help='Run in simulation mode')
    
    # 调试参数
    parser.add_argument('--debug', action='store_true', help='Enable debug mode')
    parser.add_argument('--save_dir', type=str, default='checkpoints/real_game', help='Save directory')
    
    return parser.parse_args()


def create_game_assistant(args):
    """创建游戏助手"""
    if args.simulate_mode or not GAME_ASSISTANT_AVAILABLE:
        print("🔧 运行在模拟模式")
        return None
    
    print(f"🎮 尝试连接游戏窗口: {args.game_window}")
    print(f"📁 模型路径: {args.model_path}")
    print(f"📁 模型文件存在: {os.path.exists(args.model_path)}")
    
    try:
        # 初始化游戏助手
        assist = AssistBasicToolkit()
        hwnd = assist.register_and_bind_window_objects(args.game_window)
        assist.detection.load_model(args.model_path)
        
        game_assistant = TripleEliminationGameAssistant.created(hwnd, assist)
        
        print(f"✅ 成功连接到游戏窗口: {args.game_window}")
        return game_assistant
        
    except Exception as e:
        print(f"❌ 创建游戏助手失败: {e}")
        print("🔧 切换到模拟模式")
        return None


def create_environment(args, game_assistant):
    """创建游戏环境"""
    print("🏗️ 创建游戏环境...")
    
    env = ClickMatch3Env(
        game_assistant=game_assistant,
        max_steps=args.max_steps,
        n_colors=args.n_colors,
        storage_capacity=args.storage_capacity
    )
    
    print(f"✅ 环境创建成功:")
    print(f"   观察空间: {env.observation_space.shape}")
    print(f"   动作空间: {env.action_space.n}")
    print(f"   最大步数: {args.max_steps}")
    print(f"   模式: {'真实游戏' if game_assistant else '模拟模式'}")
    
    return env


def create_agent(args, env):
    """创建PPO智能体"""
    print("🤖 创建PPO智能体...")
    
    state_dim = env.observation_space.shape[0]
    action_dim = env.action_space.n
    
    agent = PPOAgent(
        state_dim=state_dim,
        action_dim=action_dim,
        lr=args.lr,
        gamma=args.gamma,
        epsilon=args.clip_epsilon,
        hidden_dim=args.hidden_dims[0] if args.hidden_dims else 256
    )
    
    print(f"✅ 智能体创建成功:")
    print(f"   状态维度: {state_dim}")
    print(f"   动作维度: {action_dim}")
    print(f"   网络结构: {args.hidden_dims}")
    print(f"   学习率: {args.lr}")
    
    return agent


class SimpleBuffer:
    """简单的经验缓冲区"""
    
    def __init__(self):
        self.states = []
        self.actions = []
        self.rewards = []
        self.log_probs = []
        self.values = []
        self.dones = []
    
    def store(self, state, action, reward, log_prob, value, done):
        self.states.append(state)
        self.actions.append(action)
        self.rewards.append(reward)
        self.log_probs.append(log_prob)
        self.values.append(value)
        self.dones.append(done)
    
    def get_batch(self):
        return {
            'states': np.array(self.states),
            'actions': np.array(self.actions),
            'rewards': np.array(self.rewards),
            'old_log_probs': np.array(self.log_probs),
            'values': np.array(self.values),
            'dones': np.array(self.dones)
        }
    
    def clear(self):
        self.states.clear()
        self.actions.clear()
        self.rewards.clear()
        self.log_probs.clear()
        self.values.clear()
        self.dones.clear()


def compute_returns_and_advantages(rewards, values, dones, gamma=0.99, lam=0.95):
    """计算回报和优势"""
    returns = []
    advantages = []
    
    # 计算回报
    R = 0
    for i in reversed(range(len(rewards))):
        if dones[i]:
            R = 0
        R = rewards[i] + gamma * R
        returns.insert(0, R)
    
    # 计算优势
    advantages = np.array(returns) - np.array(values)
    
    return np.array(returns), advantages


def train_episode(env, agent, buffer, args):
    """训练一个episode"""
    state = env.reset()
    episode_reward = 0
    episode_length = 0
    
    while True:
        # 智能体选择动作
        if hasattr(agent, 'select_action'):
            result = agent.select_action(state)
            if isinstance(result, tuple):
                action, log_prob = result
            else:
                action = result
                log_prob = 0.0
        else:
            action = np.random.randint(0, env.action_space.n)
            log_prob = 0.0
        
        # 获取状态价值（如果智能体支持）
        if hasattr(agent, 'get_value'):
            value = agent.get_value(state)
        else:
            value = 0.0
        
        # 执行动作
        next_state, reward, done, info = env.step(action)
        
        # 存储经验
        buffer.store(state, action, reward, log_prob, value, done)
        
        # 更新状态
        state = next_state
        episode_reward += reward
        episode_length += 1
        
        if done:
            break
    
    return episode_reward, episode_length


def evaluate_agent(env, agent, num_episodes=3):
    """评估智能体"""
    total_reward = 0
    total_length = 0
    
    for i in range(num_episodes):
        state = env.reset()
        episode_reward = 0
        episode_length = 0
        
        while True:
            # 使用确定性策略
            if hasattr(agent, 'select_action'):
                action = agent.select_action(state, deterministic=True)
                if isinstance(action, tuple):
                    action = action[0]
            else:
                action = np.random.randint(0, env.action_space.n)
            
            state, reward, done, _ = env.step(action)
            episode_reward += reward
            episode_length += 1
            
            if done:
                break
        
        total_reward += episode_reward
        total_length += episode_length
    
    return total_reward / num_episodes, total_length / num_episodes


def main():
    """主函数"""
    args = parse_args()
    
    print("=" * 60)
    print("🚀 强化学习训练点击式三消游戏 (修复版)")
    print("=" * 60)
    print(f"📊 训练参数:")
    print(f"   回合数: {args.num_episodes}")
    print(f"   最大步数: {args.max_steps}")
    print(f"   学习率: {args.lr}")
    print(f"   调试模式: {args.debug}")
    print()
    
    # 创建保存目录
    os.makedirs(args.save_dir, exist_ok=True)
    
    # 创建游戏助手
    game_assistant = create_game_assistant(args)
    
    # 创建环境
    env = create_environment(args, game_assistant)
    
    # 创建智能体
    agent = create_agent(args, env)
    
    # 创建缓冲区
    buffer = SimpleBuffer()
    
    print("\n🎯 开始训练...")
    print("-" * 40)
    
    # 训练循环
    results = {'episodes': []}
    
    try:
        for episode in range(args.num_episodes):
            start_time = time.time()
            
            # 训练一个episode
            episode_reward, episode_length = train_episode(env, agent, buffer, args)
            
            # 记录结果
            results['episodes'].append({
                'reward': episode_reward,
                'length': episode_length
            })
            
            # 记录日志
            if episode % args.log_frequency == 0:
                elapsed_time = time.time() - start_time
                print(f"Episode {episode:3d}: Reward={episode_reward:7.2f}, Length={episode_length:3d}, Time={elapsed_time:.2f}s")
                
                if args.debug:
                    env.render()
            
            # 定期评估
            if episode % args.eval_frequency == 0 and episode > 0:
                avg_reward, avg_length = evaluate_agent(env, agent, 3)
                print(f"📊 评估 Episode {episode}: 平均奖励={avg_reward:.2f}, 平均长度={avg_length:.1f}")
            
            # 清空缓冲区（简化版本，实际PPO需要批量更新）
            buffer.clear()
    
    except KeyboardInterrupt:
        print("\n⏹️ 训练被用户中断")
    
    # 最终评估
    print("\n🏁 最终评估:")
    final_reward, final_length = evaluate_agent(env, agent, 5)
    print(f"   平均奖励: {final_reward:.2f}")
    print(f"   平均长度: {final_length:.1f}")
    
    # 关闭环境
    env.close()
    
    print("\n✅ 训练完成!")


if __name__ == "__main__":
    main()
