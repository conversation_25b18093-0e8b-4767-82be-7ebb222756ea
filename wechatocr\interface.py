"""
微信OCR调用层模块

该模块对应用层进行进一步封装，提供三种调用方式的统一接口：
1. 同步识别方式调用（WeChatSyncOCR）- 简单场景
2. 高效任务方式调用（WeChatTasksOCR）- 批量处理场景
3. 高性能异步方式调用（WeChatAsyncOCR）- 高并发场景

每种调用方式都针对特定场景进行了优化，用户可以根据需求选择合适的调用方式。
"""

from typing import Union, Dict, List, Any, Optional, Tuple
import numpy as np
from PIL import Image

from wechatocr.base import OCREngine, OCRResultFormatter

from wechatocr.application import SyncOCR, TaskOCR, AsyncOCR


class WeChatSyncOCR:
    """
    微信OCR同步调用接口
    
    适用于简单场景，直接调用即可获取结果。
    特点：
    - 简单易用，直接调用即可获取结果
    - 同步阻塞，适合单次识别
    - 资源占用少，适合轻量级应用
    
    使用方法：
    ```python
    # 初始化
    ocr = WeChatSyncOCR()
    
    # 识别图片
    result = ocr.recognize("image.jpg")
    
    # 获取文本列表
    texts = ocr.get_texts(result)
    
    # 关闭
    ocr.close()
    ```
    """
    
    def __init__(self, engine: OCREngine = None):
        """初始化同步OCR调用接口"""
        self.ocr = SyncOCR(engine)
    
    def recognize(self, img_input: Union[str, np.ndarray, Image.Image]) -> Dict[str, Any]:
        """
        识别图片
        :param img_input: 图片输入（路径、OpenCV图像或PIL图像）
        :return: OCR识别结果
        """
        return self.ocr.recognize(img_input)
    
    def recognize_roi(self, img_input: Union[str, np.ndarray, Image.Image], roi: Tuple[int, int, int, int]) -> Dict[str, Any]:
        """
        识别图片中的指定区域
        :param img_input: 图片输入
        :param roi: 感兴趣区域，格式(left, upper, right, lower)
        :return: OCR识别结果
        """
        return self.ocr.recognize_roi(img_input, roi)
    
    def batch_recognize(self, img_list: List[Union[str, np.ndarray, Image.Image]]) -> List[Dict[str, Any]]:
        """
        批量识别图片
        :param img_list: 图片列表
        :return: OCR识别结果列表
        """
        return self.ocr.batch_recognize(img_list)
    
    def get_texts(self, result: Dict[str, Any]) -> List[str]:
        """
        从OCR结果中提取文本列表
        :param result: OCR识别结果
        :return: 文本列表
        """
        return self.ocr.format_result(result)
    
    @property
    def result_formatter(self) -> OCRResultFormatter:
        """获取OCR结果格式化器"""
        return self.ocr.result_formatter
            
    def close(self):
        """关闭OCR引擎"""
        self.ocr.close()


class WeChatTasksOCR:
    """
    微信OCR任务调用接口
    
    适用于批量处理场景，支持任务管理和异步提交。
    特点：
    - 任务队列管理，支持批量处理
    - 资源复用，提高处理效率
    - 支持任务状态查询和结果获取
    
    使用方法：
    ```python
    # 初始化
    ocr = WeChatTasksOCR()
    
    # 同步识别
    result1 = ocr.recognize("image1.jpg")
    
    # 异步提交任务
    task_id = ocr.submit_task("image2.jpg")
    
    # 获取任务结果
    result2 = ocr.get_result(task_id)
    
    # 批量识别
    results = ocr.batch_recognize(["image3.jpg", "image4.jpg"])
    
    # 关闭
    ocr.close()
    ```
    """
    
    def __init__(self, engine: OCREngine = None):
        """初始化任务OCR调用接口"""
        self.ocr = TaskOCR(engine)
    
    def recognize(self, img_input: Union[str, np.ndarray, Image.Image]) -> Dict[str, Any]:
        """
        同步识别图片
        :param img_input: 图片输入
        :return: OCR识别结果
        """
        return self.ocr.recognize(img_input)
    
    def submit_task(self, img_input: Union[str, np.ndarray, Image.Image]) -> str:
        """
        异步提交OCR任务
        :param img_input: 图片输入
        :return: 任务ID
        """
        return self.ocr.recognize_async(img_input)
    
    def get_result(self, task_id: str, wait: bool = True, timeout: Optional[float] = None) -> Optional[Dict[str, Any]]:
        """
        获取任务结果
        :param task_id: 任务ID
        :param wait: 是否等待任务完成
        :param timeout: 等待超时时间
        :return: OCR识别结果
        """
        return self.ocr.get_result(task_id, wait, timeout)
    
    def batch_recognize(self, img_list: List[Union[str, np.ndarray, Image.Image]]) -> List[Dict[str, Any]]:
        """
        批量识别图片
        :param img_list: 图片列表
        :return: OCR识别结果列表
        """
        return self.ocr.batch_recognize(img_list)
    
    def get_texts(self, result: Dict[str, Any]) -> List[str]:
        """
        从OCR结果中提取文本列表
        :param result: OCR识别结果
        :return: 文本列表
        """
        return self.ocr.format_result(result)
    
    @property
    def result_formatter(self) -> OCRResultFormatter:
        """获取OCR结果格式化器"""
        return self.ocr.result_formatter

    def close(self):
        """关闭OCR引擎"""
        self.ocr.close()


class WeChatAsyncOCR:
    """
    微信OCR异步调用接口
    
    适用于高并发场景，基于asyncio的异步处理。
    特点：
    - 异步处理，不阻塞主线程
    - 高并发支持，适合大量图片处理
    - 资源高效利用
    
    使用方法：
    ```python
    # 初始化
    ocr = WeChatAsyncOCR()
    
    # 异步识别
    async def main():
        result = await ocr.recognize("image.jpg")
        texts = ocr.get_texts(result)
        
        # 批量识别
        results = await ocr.batch_recognize(["image1.jpg", "image2.jpg"])
        
        # 关闭
        ocr.close()
    
    # 运行异步函数
    asyncio.run(main())
    ```
    """
    
    def __init__(self, engine: OCREngine = None):
        """初始化异步OCR调用接口"""
        self.ocr = AsyncOCR(engine)
    
    async def recognize(self, img_input: Union[str, np.ndarray, Image.Image]) -> Dict[str, Any]:
        """
        异步识别图片
        :param img_input: 图片输入
        :return: OCR识别结果
        """
        return await self.ocr.recognize(img_input)
    
    async def batch_recognize(self, img_list: List[Union[str, np.ndarray, Image.Image]]) -> List[Dict[str, Any]]:
        """
        异步批量识别图片
        :param img_list: 图片列表
        :return: OCR识别结果列表
        """
        return await self.ocr.batch_recognize(img_list)
    
    def get_texts(self, result: Dict[str, Any]) -> List[str]:
        """
        从OCR结果中提取文本列表
        :param result: OCR识别结果
        :return: 文本列表
        """
        return self.ocr.format_result(result)
    
    @property
    def result_formatter(self) -> OCRResultFormatter:
        """获取OCR结果格式化器"""
        return self.ocr.result_formatter

    def close(self):
        """关闭OCR引擎"""
        self.ocr.close()


# 为了向后兼容，提供一个统一的WeChatOCR类
class WeChatOCR:
    """
    微信OCR统一调用接口（向后兼容）
    
    默认使用同步调用方式，兼容旧版本代码。
    """
    
    def __init__(self, engine: OCREngine = None):
        """初始化OCR调用接口"""
        self.ocr = WeChatSyncOCR(engine)
    
    def recognize(self, img_input: Union[str, np.ndarray, Image.Image]) -> Dict[str, Any]:
        """识别图片"""
        return self.ocr.recognize(img_input)
    
    def recognize_roi(self, img_input: Union[str, np.ndarray, Image.Image], roi: Tuple[int, int, int, int]) -> Dict[str, Any]:
        """识别图片中的指定区域"""
        return self.ocr.recognize_roi(img_input, roi)
    
    def batch_recognize(self, img_list: List[Union[str, np.ndarray, Image.Image]]) -> List[Dict[str, Any]]:
        """批量识别图片"""
        return self.ocr.batch_recognize(img_list)
    
    def format_result(self, raw_result: Dict[str, Any]) -> List[str]:
        """格式化OCR结果"""
        return self.ocr.get_texts(raw_result)
    
    @property
    def result_formatter(self) -> OCRResultFormatter:
        """获取OCR结果格式化器"""
        return self.ocr.result_formatter

    def close(self):
        """关闭OCR引擎"""
        self.ocr.close()
    
    def __del__(self):
        """析构时关闭OCR引擎"""
        self.close()
