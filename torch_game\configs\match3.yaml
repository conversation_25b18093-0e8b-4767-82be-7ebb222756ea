# Match-3游戏专用配置文件

# 继承默认配置
inherit: "default.yaml"

# 环境配置
env:
  type: "Match3Env"
  board_size: 8       # 更大的棋盘
  num_colors: 6       # 更多的颜色
  min_match: 3        # 最小匹配数
  reward_scale: 1.0   # 奖励缩放
  reward_configs:
    match_3: 10       # 3连奖励
    match_4: 20       # 4连奖励
    match_5: 50       # 5连奖励
    combo_multiplier: 1.5  # 连击倍数

# 智能体配置
agent:
  type: "PPOAgent"
  hidden_dims: [512, 256]  # 更大的网络
  lr: 0.0001              # 更小的学习率
  gamma: 0.995            # 更大的折扣因子
  gae_lambda: 0.97        # 更大的GAE lambda
  clip_epsilon: 0.15      # 更小的裁剪参数
  entropy_coef: 0.01      # 熵系数
  value_coef: 0.5         # 价值系数
  max_grad_norm: 0.5      # 梯度裁剪
  target_kl: 0.01         # 目标KL散度
  
  # 网络配置
  network:
    activation: "ReLU"
    use_batch_norm: true
    dropout: 0.1
    initialization: "orthogonal"

# 训练配置
training:
  num_episodes: 5000     # 更多的训练回合
  max_steps: 200         # 更长的回合
  buffer_capacity: 50000 # 更大的缓冲区
  batch_size: 128        # 更大的批次
  update_frequency: 8    # 更频繁的更新
  num_epochs: 4          # PPO更新轮数
  
  # 学习率调度
  lr_schedule:
    type: "linear"
    start_factor: 1.0
    end_factor: 0.1
    total_iters: 5000
  
  # 早停策略
  early_stopping:
    patience: 50
    min_delta: 0.01
    
  # 检查点配置
  checkpointing:
    frequency: 100       # 每100回合保存一次
    keep_best: 5         # 保留最好的5个模型
    metrics: ["reward", "length"]

# 评估配置
evaluation:
  num_episodes: 50      # 更多的评估回合
  render: true
  delay: 0.3            # 更快的渲染
  metrics: ["reward", "length", "matches", "combos"]

# 日志配置
logging:
  level: "INFO"
  save_to_file: true
  log_dir: "logs/match3"
  tensorboard: true     # 使用TensorBoard
  wandb:                # Weights & Biases配置
    enabled: false
    project: "match3"
    entity: "your-username"