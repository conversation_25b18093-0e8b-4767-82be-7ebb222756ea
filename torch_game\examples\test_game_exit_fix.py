"""
测试游戏退出处理修复的脚本
验证放弃复活确定按钮的处理逻辑
"""

import os
import sys
import time

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

print("🧪 测试游戏退出处理修复")
print("=" * 50)

def test_game_assistant_exit_handling():
    """测试游戏助手的退出处理"""
    print("\n📋 测试游戏助手退出处理逻辑")
    
    try:
        from s4_三消 import TripleEliminationGameAssistant
        from assist import AssistBasicToolkit
        
        # 创建游戏助手
        assist = AssistBasicToolkit()
        hwnd = assist.register_and_bind_window_objects("最强祖师")
        assist.detection.load_model(r"models\sanxiao\best.pt")
        
        game_assistant = TripleEliminationGameAssistant.created(hwnd, assist)
        print("✅ 游戏助手创建成功")
        
        # 检查是否加载了确认图片
        if hasattr(game_assistant, 'game_fail_confirm_image'):
            print("✅ 放弃复活确定图片已加载")
        else:
            print("❌ 放弃复活确定图片未加载")
            return False
        
        # 测试截图和检测
        print("📸 测试截图和退出检测...")
        current_img = game_assistant.screenshot.capture_client_area()
        
        if current_img is None:
            print("❌ 截图失败")
            return False
        
        print(f"✅ 截图成功: {current_img.size}")
        
        # 测试退出检测（不会实际点击，只是检测）
        exit_detected = game_assistant.check_and_handle_game_exit(current_img)
        print(f"🔍 退出状态检测结果: {exit_detected}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_click_match3_env_reset():
    """测试ClickMatch3Env的重置逻辑"""
    print("\n📋 测试ClickMatch3Env重置逻辑")
    
    try:
        from torch_game.core.env import ClickMatch3Env
        from s4_三消 import TripleEliminationGameAssistant
        from assist import AssistBasicToolkit
        
        # 创建游戏助手
        assist = AssistBasicToolkit()
        hwnd = assist.register_and_bind_window_objects("最强祖师")
        assist.detection.load_model(r"models\sanxiao\best.pt")
        game_assistant = TripleEliminationGameAssistant.created(hwnd, assist)
        
        # 创建环境
        env = ClickMatch3Env(
            game_assistant=game_assistant,
            max_steps=5,
            n_colors=6,
            storage_capacity=7
        )
        print("✅ 环境创建成功")
        
        # 测试正常重置
        print("🔄 测试正常重置...")
        observation = env.reset()
        print(f"✅ 正常重置成功，观察值形状: {observation.shape}")
        
        # 模拟游戏结束情况
        print("🎮 模拟游戏结束情况...")
        env._need_game_restart = True
        
        # 测试游戏重启重置
        print("🔄 测试游戏重启重置...")
        observation = env.reset()
        print(f"✅ 游戏重启重置成功，观察值形状: {observation.shape}")
        
        # 关闭环境
        env.close()
        print("✅ 环境关闭成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_image_files():
    """测试图片文件是否存在"""
    print("\n📋 测试图片文件")
    
    image_files = [
        r"models\sanxiao\放弃复活.png",
        r"models\sanxiao\放弃复活确定.png",
        r"models\sanxiao\游戏失败.png",
        r"models\sanxiao\点击关闭屏幕.png"
    ]
    
    all_exist = True
    for image_file in image_files:
        if os.path.exists(image_file):
            print(f"✅ {image_file} 存在")
        else:
            print(f"❌ {image_file} 不存在")
            all_exist = False
    
    return all_exist


def main():
    """主测试函数"""
    print("🚀 开始测试游戏退出处理修复")
    print("\n⚠️  请确保:")
    print("   1. '最强祖师'游戏正在运行")
    print("   2. 游戏处于可操作状态")
    
    input("\n按Enter键继续...")
    
    # 测试1: 图片文件检查
    print("\n" + "="*50)
    if not test_image_files():
        print("❌ 图片文件检查失败，无法继续测试")
        return
    
    # 测试2: 游戏助手退出处理
    print("\n" + "="*50)
    if not test_game_assistant_exit_handling():
        print("❌ 游戏助手退出处理测试失败")
        return
    
    # 测试3: 环境重置逻辑
    print("\n" + "="*50)
    if not test_click_match3_env_reset():
        print("❌ 环境重置逻辑测试失败")
        return
    
    print("\n" + "="*60)
    print("🎉 所有测试通过！")
    print("✅ 游戏退出处理修复验证成功")
    print("\n💡 修复内容:")
    print("   1. 添加了放弃复活确定按钮的处理")
    print("   2. 改进了游戏重启等待逻辑")
    print("   3. 增强了错误处理和重试机制")
    print("\n🚀 现在可以运行完整的训练:")
    print("   python torch_game\\examples\\train_real_game_fixed.py --game_window \"最强祖师\" --model_path \"models\\sanxiao\\best.pt\"")


if __name__ == "__main__":
    main()
