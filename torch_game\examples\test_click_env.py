"""
测试ClickMatch3Env环境的脚本
验证环境是否正常工作，包括状态获取、动作执行、奖励计算等
"""

import os
import sys
import numpy as np

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from torch_game.core.env import ClickMatch3Env


def test_environment_basic():
    """测试环境基础功能"""
    print("=" * 50)
    print("测试ClickMatch3Env基础功能")
    print("=" * 50)
    
    # 创建环境（模拟模式）
    env = ClickMatch3Env(
        game_assistant=None,  # 模拟模式
        max_steps=50,
        n_colors=6,
        storage_capacity=7
    )
    
    print(f"✓ 环境创建成功")
    print(f"  观察空间形状: {env.observation_space.shape}")
    print(f"  动作空间大小: {env.action_space.n}")
    print(f"  最大步数: {env.max_steps}")
    
    # 测试重置
    observation = env.reset()
    print(f"✓ 环境重置成功")
    print(f"  初始观察值形状: {observation.shape}")
    print(f"  初始观察值范围: [{observation.min():.3f}, {observation.max():.3f}]")
    
    # 测试步进
    total_reward = 0
    for step in range(10):
        action = env.action_space.sample()  # 随机动作
        observation, reward, done, info = env.step(action)
        total_reward += reward
        
        print(f"  步骤 {step + 1}: 动作={action}, 奖励={reward:.2f}, 结束={done}")
        
        if done:
            print(f"  回合结束于步骤 {step + 1}")
            break
    
    print(f"✓ 环境步进测试完成")
    print(f"  总奖励: {total_reward:.2f}")
    
    return True


def test_state_converter():
    """测试状态转换器"""
    print("\n" + "=" * 50)
    print("测试StateConverter")
    print("=" * 50)
    
    from torch_game.core.env.click_match3_env import StateConverter
    
    converter = StateConverter(n_colors=6, storage_capacity=7)
    
    # 模拟方块数据
    main_blocks = {
        0: [{'position': (100, 100)}, {'position': (150, 100)}],
        1: [{'position': (200, 100)}],
        2: [{'position': (100, 150)}, {'position': (150, 150)}, {'position': (200, 150)}]
    }
    
    storage_blocks = {
        0: [{'position': (50, 300)}],
        1: [{'position': (100, 300)}, {'position': (150, 300)}]
    }
    
    observation = converter.convert_to_observation(
        main_blocks, storage_blocks, current_step=10, max_steps=100
    )
    
    print(f"✓ 状态转换成功")
    print(f"  观察值形状: {observation.shape}")
    print(f"  观察值范围: [{observation.min():.3f}, {observation.max():.3f}]")
    print(f"  非零元素数量: {np.count_nonzero(observation)}")
    
    return True


def test_action_mapper():
    """测试动作映射器"""
    print("\n" + "=" * 50)
    print("测试ActionMapper")
    print("=" * 50)
    
    from torch_game.core.env.click_match3_env import ActionMapper
    
    mapper = ActionMapper()
    
    # 模拟方块数据
    main_blocks = {
        0: [
            {'class_id': 0, 'position': (100, 100), 'name': 'block_0'},
            {'class_id': 0, 'position': (150, 100), 'name': 'block_0'}
        ],
        1: [
            {'class_id': 1, 'position': (200, 100), 'name': 'block_1'}
        ]
    }
    
    # 测试有效动作
    for action in range(5):
        block = mapper.map_action_to_block(action, main_blocks)
        if block:
            print(f"  动作 {action} -> 方块: {block['name']} at {block['position']}")
        else:
            print(f"  动作 {action} -> 无效动作")
    
    print(f"✓ 动作映射测试完成")
    
    return True


def test_reward_calculator():
    """测试奖励计算器"""
    print("\n" + "=" * 50)
    print("测试RewardCalculator")
    print("=" * 50)
    
    from torch_game.core.env.click_match3_env import RewardCalculator
    
    calculator = RewardCalculator()
    
    # 测试场景1：成功消除
    prev_main_blocks = {0: [{'id': 1}, {'id': 2}]}
    prev_storage_blocks = {0: [{'id': 3}, {'id': 4}, {'id': 5}]}  # 3个相同方块
    
    new_main_blocks = {0: [{'id': 1}, {'id': 2}]}
    new_storage_blocks = {}  # 消除后为空
    
    reward1 = calculator.calculate_reward(
        prev_main_blocks, prev_storage_blocks,
        new_main_blocks, new_storage_blocks,
        action_success=True, action_info={}
    )
    
    print(f"  场景1 - 成功消除: 奖励 = {reward1:.2f}")
    
    # 测试场景2：无效动作
    reward2 = calculator.calculate_reward(
        prev_main_blocks, prev_storage_blocks,
        prev_main_blocks, prev_storage_blocks,
        action_success=False, action_info={'error': 'invalid'}
    )
    
    print(f"  场景2 - 无效动作: 奖励 = {reward2:.2f}")
    
    # 测试场景3：预存区接近满
    full_storage_blocks = {i: [{'id': i}] for i in range(6)}  # 6个方块
    
    reward3 = calculator.calculate_reward(
        prev_main_blocks, prev_storage_blocks,
        prev_main_blocks, full_storage_blocks,
        action_success=True, action_info={}
    )
    
    print(f"  场景3 - 预存区接近满: 奖励 = {reward3:.2f}")
    
    print(f"✓ 奖励计算测试完成")
    
    return True


def test_full_episode():
    """测试完整回合"""
    print("\n" + "=" * 50)
    print("测试完整回合")
    print("=" * 50)
    
    env = ClickMatch3Env(
        game_assistant=None,
        max_steps=20,
        n_colors=6,
        storage_capacity=7
    )
    
    observation = env.reset()
    total_reward = 0
    step_count = 0
    
    print("开始回合...")
    
    while True:
        action = env.action_space.sample()
        observation, reward, done, info = env.step(action)
        total_reward += reward
        step_count += 1
        
        if step_count % 5 == 0:
            print(f"  步骤 {step_count}: 累积奖励 = {total_reward:.2f}")
            env.render()  # 显示状态信息
        
        if done:
            break
    
    print(f"✓ 回合完成")
    print(f"  总步数: {step_count}")
    print(f"  总奖励: {total_reward:.2f}")
    print(f"  平均奖励: {total_reward/step_count:.2f}")
    
    return True


def run_all_tests():
    """运行所有测试"""
    print("开始测试ClickMatch3Env环境")
    print("时间:", np.datetime64('now'))
    
    tests = [
        ("基础功能", test_environment_basic),
        ("状态转换器", test_state_converter),
        ("动作映射器", test_action_mapper),
        ("奖励计算器", test_reward_calculator),
        ("完整回合", test_full_episode)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            print(f"\n正在运行测试: {test_name}")
            result = test_func()
            if result:
                print(f"✓ {test_name} 测试通过")
                passed += 1
            else:
                print(f"✗ {test_name} 测试失败")
                failed += 1
        except Exception as e:
            print(f"✗ {test_name} 测试出错: {e}")
            failed += 1
    
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    print(f"通过: {passed}")
    print(f"失败: {failed}")
    print(f"总计: {passed + failed}")
    
    if failed == 0:
        print("🎉 所有测试都通过了！")
        return True
    else:
        print("❌ 有测试失败，请检查代码")
        return False


if __name__ == "__main__":
    success = run_all_tests()
    exit(0 if success else 1)
