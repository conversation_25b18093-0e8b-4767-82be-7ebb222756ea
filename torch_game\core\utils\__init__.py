"""
工具模块
包含各种辅助工具，如经验回放缓冲区、配置管理和训练器
"""

from .buffer import ReplayBuffer
from .config import Config

# TODO: 实现ConfigManager类
class ConfigManager:
    """临时的配置管理器，待完善"""
    @staticmethod
    def load(config_path):
        """加载配置文件"""
        import yaml
        with open(config_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)

# TODO: Trainer类需要修复导入错误后再导入
# from .trainer import Trainer

__all__ = ['ReplayBuffer', 'Config', 'ConfigManager']