"""
窗口操作相关接口封装
"""
import ctypes
ctypes.windll.shcore.SetProcessDpiAwareness(2)  # 高DPI感知

import win32gui
import win32api
import win32con
import psutil
import win32process
from typing import Tuple, List

class WindowOps:

    # 窗口查找
    def find_window(self, cls_name: str, title: str):
        '''
        查找符合类名和标题的窗口，返回窗口句柄        
        '''
        hwnd = win32gui.FindWindow(cls_name, title)
        if hwnd == 0:
            raise Exception("未找到窗口")
        return hwnd
    
    def find_window_by_partial_title(self, title: str):
        '''
        查找符合部分标题的窗口，返回窗口句柄
        '''
        def enum_callback(hwnd, result):
            window_text = win32gui.GetWindowText(hwnd)
            if title in window_text:
                result.append(hwnd)
            return True
        found_hwnds = []
        win32gui.EnumWindows(enum_callback, found_hwnds)
        if not found_hwnds:
            return
        return found_hwnds[0]  # 假设只有一个匹配的窗口，返回第一个

    def find_child_window(self, parent: int=0, cls_name: str='', title: str='', require_visible: bool=True) -> Tuple[int, str, str]:
        '''
        查找子窗口
        :param parent: 父窗口句柄
        :param cls_name: 窗口类名（可选）
        :param title: 窗口标题（可选）
        :param require_visible: 是否要求窗口可见
        :return: 返回第一个匹配的子窗口信息，如果没有找到则返回 None
        '''
        child_windows = self.enum_child_windows(parent, cls_name, title, require_visible)
        if not child_windows:
            return
        return child_windows[0]  # 假设只有一个匹配的窗口，返回第一个

    def enum_child_windows(self, parent: int=0, cls_name: str='', title: str='', require_visible: bool=True) -> List[Tuple[int, str, str]]:
        '''
        查找所有符合条件的子窗口
        :param parent: 父窗口句柄
        :param cls_name: 窗口类名（可选）
        :param title: 窗口标题（可选）
        :param require_visible: 是否要求窗口可见
        :return: 返回所有符合条件的子窗口信息列表，每个元素为元组 (hwnd, class_name, window_title)
        '''
        def enum_callback(hwnd, result):
            if require_visible and not win32gui.IsWindowVisible(hwnd):
                return True
            if self.find_parent_window(hwnd) != parent:
                return True
            window_text = win32gui.GetWindowText(hwnd)
            window_class = win32gui.GetClassName(hwnd)
            if (cls_name in window_class or cls_name == '') and (title in window_text or title == ''):
                result.append((hwnd, window_class, window_text))
            return True
        found_windows = []
        win32gui.EnumChildWindows(parent, enum_callback, found_windows)
        return found_windows

    def find_parent_window(self, hwnd: int):
        '''
        获取父窗口句柄
        '''
        return win32gui.GetParent(hwnd)

    def find_window_by_process_name(self, process_name: str):
        '''
        根据指定的进程名字，来查找可见窗口
        '''
        def enum_callback(hwnd, result):
            if win32gui.IsWindowVisible(hwnd):
                _, found_process_name = win32process.GetWindowThreadProcessId(hwnd)
                if process_name in found_process_name:
                    result.append(hwnd)
            return True
        found_hwnds = []
        win32gui.EnumWindows(enum_callback, found_hwnds)
        if not found_hwnds:
            return
        return found_hwnds[0]  # 假设只有一个匹配的窗口，返回第一个

    def find_window_by_process_id(self, process_id: int):
        '''
        根据指定的进程id，来查找可见窗口
        '''
        def enum_callback(hwnd, result):
            if win32gui.IsWindowVisible(hwnd):
                _, found_process_id = win32process.GetWindowThreadProcessId(hwnd)
                if process_id == found_process_id:
                    result.append(hwnd)
            return True
        found_hwnds = []
        win32gui.EnumWindows(enum_callback, found_hwnds)
        if not found_hwnds:
            return
        return found_hwnds[0]  # 假设只有一个匹配的窗口，返回第一个
    
    def get_foreground_window(self):
        '''
        获取顶层活动窗口, 与get_foreground_focus()不同的是，这个函数返回的是窗口句柄，而get_foreground_focus()返回的是活动窗口的句柄
        '''
        return win32gui.GetForegroundWindow()
    
    def get_foreground_focus(self):
        '''
        获取顶层活动窗口的焦点窗口
        '''
        return win32gui.GetFocus()
    
    def get_mouse_point_window(self):
        '''
        获取鼠标指向的可见窗口句柄
        '''
        return win32gui.WindowFromPoint(win32api.GetCursorPos())
    
    def get_point_window(self, point: Tuple[int, int]):
        '''
        获取指定坐标点的窗口句柄
        '''
        return win32gui.WindowFromPoint(point)
    

    # 获取窗口信息
    def get_window_rect(self, hwnd: int):
        '''
        获取窗口的屏幕坐标，左上角和右下角的坐标，(x, y, right, bottom)
        '''
        return win32gui.GetWindowRect(hwnd)
    
    def get_window_size(self, hwnd: int):
        '''
        获取窗口的尺寸，返回的是窗口的宽和高，width, height
        '''
        rect = self.get_window_rect(hwnd)
        return rect[2] - rect[0], rect[3] - rect[1]
        
    def get_client_rect(self, hwnd: int):
        '''
        获取窗口客户区矩形区域，左上角坐标及宽高，左上角总是(0, 0)，width, height (x, y, width, height)
        '''
        return win32gui.GetClientRect(hwnd)

    def get_client_size(self, hwnd: int):
        '''
        获取窗口客户区的尺寸
        '''
        return self.get_client_rect(hwnd)[2:]
    
    def get_client_pos(self, hwnd: int):
        '''
        获取窗口客户区左上角的坐标
        '''
        x, y = self.client_to_screen(hwnd, (0, 0))
        width, height = self.get_client_size(hwnd)
        right, bottom = width + x, height + y
        return x, y, right, bottom
    
    def get_window_title(self, hwnd: int):
        '''
        获取窗口的标题
        '''
        return win32gui.GetWindowText(hwnd)

    def get_window_class(self, hwnd: int):
        '''
        获取窗口的类名
        '''
        return win32gui.GetClassName(hwnd)

    def get_window_process(self, hwnd: int):
        '''
        获取窗口的进程id和进程名
        '''
        _, process_id = win32process.GetWindowThreadProcessId(hwnd)
        p = psutil.Process(process_id)
        print(f"进程名: {p.name()}")
        print(f"完整路径: {p.exe()}")
        return process_id, p.name()

    def get_window_process_path(self, hwnd: int):
        '''
        获取窗口的进程路径
        '''
        _, process_id = win32process.GetWindowThreadProcessId(hwnd)
        process_path = win32process.GetModuleFileNameEx(process_id)
        return process_path

    def get_system_metrics(self, nIndex):
        '''
        获取系统信息
        '''
        return win32api.GetSystemMetrics(nIndex)

    def get_screen_size(self):
        '''
        获取屏幕尺寸
        '''
        return win32api.GetSystemMetrics(0), win32api.GetSystemMetrics(1)

    # 窗口操作
    def set_window_rect(self, hwnd: int, rect: Tuple[int, int, int, int]):
        '''
        设置窗口的大小
        '''
        win32gui.MoveWindow(hwnd, rect[0], rect[1], rect[2] - rect[0], rect[3] - rect[1], True)

    def set_window_state(self, hwnd: int, state: int):
        '''
        设置窗口的状态,根据state从state_lambda中获取
        '''
        state_lambda = {
            0: win32gui.SW_SHOWNORMAL,  # 正常显示
            1: win32gui.SW_SHOWMINIMIZED,  # 最小化
            2: win32gui.SW_SHOWMAXIMIZED,  # 最大化
            3: win32gui.SW_SHOWNOACTIVATE,  # 不激活
            4: win32gui.SW_SHOW,  # 显示
            5: win32gui.SW_HIDE,  # 隐藏
            6: win32gui.SW_MINIMIZE,  # 最小化
            7: win32gui.SW_SHOWMINNOACTIVE,  # 最小化不激活
            8: win32gui.SW_SHOWNA,  # 不激活
            9: win32gui.SW_RESTORE,  # 还原
            10: win32gui.SW_SHOWDEFAULT,  # 默认
            11: win32gui.SW_FORCEMINIMIZE,  # 强制最小化
            12: win32gui.SW_MAXIMIZE,  # 最大化
        }
        win32gui.ShowWindow(hwnd, state_lambda[state])  

    def set_window_pos(self, hwnd: int, pos: Tuple[int, int]):
        '''
        设置窗口的位置
        '''
        win32gui.MoveWindow(hwnd, pos[0], pos[1], self.get_window_size(hwnd)[0], self.get_window_size(hwnd)[1], True)

    def set_window_size(self, hwnd: int, size: Tuple[int, int]):
        '''
        设置窗口的大小
        '''
        win32gui.MoveWindow(hwnd, self.get_window_pos(hwnd)[0], self.get_window_pos(hwnd)[1], size[0], size[1], True)

    def set_window_foreground(self, hwnd: int):
        '''
        将窗口设置为前台窗口
        '''
        # 显示窗口
        if win32gui.IsIconic(hwnd):  # 如果窗口最小化
            win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)
        else:
            win32gui.ShowWindow(hwnd, win32con.SW_SHOW)

        # 设置前台窗口
        win32gui.SetForegroundWindow(hwnd)

    def set_window_foreground_focus(self, hwnd: int):
        '''
        将窗口设置为前台窗口并获取焦点
        '''
        win32gui.SetForegroundWindow(hwnd)
        win32gui.SetFocus(hwnd)

    def set_window_visible(self, hwnd: int, visible: bool):
        '''
        设置窗口的可见性
        '''
        win32gui.ShowWindow(hwnd, win32gui.SW_SHOW if visible else win32gui.SW_HIDE)

    def set_window_always_on_top(self, hwnd: int, always_on_top: bool):
        '''
        设置窗口是否总是在最前面
        '''
        win32gui.SetWindowPos(
            hwnd, win32gui.HWND_TOPMOST if always_on_top else win32gui.HWND_NOTOPMOST, 0, 0, 0, 0, win32gui.SWP_NOMOVE | win32gui.SWP_NOSIZE
        )

    def set_window_always_on_bottom(self, hwnd: int, always_on_bottom: bool):
        '''
        设置窗口是否总是在最后面
        '''
        win32gui.SetWindowPos(
            hwnd, win32gui.HWND_BOTTOM if always_on_bottom else win32gui.HWND_NOTOPMOST, 0, 0, 0, 0, win32gui.SWP_NOMOVE | win32gui.SWP_NOSIZE
        )

    def set_window_topmost(self, hwnd: int, topmost: bool):
        '''
        设置窗口是否置顶
        '''
        win32gui.SetWindowPos(
            hwnd, win32gui.HWND_TOPMOST if topmost else win32gui.HWND_NOTOPMOST, 0, 0, 0, 0, win32gui.SWP_NOMOVE | win32gui.SWP_NOSIZE
        )

    def set_window_bottommost(self, hwnd: int, bottommost: bool):
        '''
        设置窗口是否置底
        '''
        win32gui.SetWindowPos(
            hwnd, win32gui.HWND_BOTTOM if bottommost else win32gui.HWND_NOTOPMOST, 0, 0, 0, 0, win32gui.SWP_NOMOVE | win32gui.SWP_NOSIZE
        )

    def set_active_window(self, hwnd: int):
        '''
        将窗口设置为活动窗口
        '''
        win32gui.SetActiveWindow(hwnd)

    def set_active_window_focus(self, hwnd: int):
        '''
        将窗口设置为活动窗口并获取焦点
        '''
        win32gui.SetActiveWindow(hwnd)
        win32gui.SetFocus(hwnd)

    def set_window_title(self, hwnd: int, title: str):
        '''
        设置窗口的标题
        '''
        win32gui.SetWindowText(hwnd, title)

    # 窗口信息校验
    def is_window_visible(self, hwnd: int):
        '''
        检查窗口是否可见
        '''
        return win32gui.IsWindowVisible(hwnd)

    def is_window_enabled(self, hwnd: int):
        '''
        检查窗口是否启用
        '''
        return win32gui.IsWindowEnabled(hwnd)

    def is_window_active(self, hwnd: int):
        '''
        检查窗口是否活动
        '''
        return win32gui.IsWindow(hwnd)

    def is_window_foreground(self, hwnd: int):
        '''
        检查窗口是否前台窗口
        '''
        return win32gui.GetForegroundWindow() == hwnd

    def is_window_foreground_focus(self, hwnd: int):
        '''
        检查窗口是否前台窗口和焦点窗口
        '''
        return win32gui.GetForegroundWindow() == hwnd and win32gui.GetFocus() == hwnd

    def is_window_topmost(self, hwnd: int):
        '''
        检查窗口是否置顶
        '''
        return win32gui.GetWindowPos(hwnd)[1] == win32gui.HWND_TOPMOST
        

    # 坐标转换
    def client_to_screen(self, hwnd: int, pos: Tuple[int, int]):
        '''
        将窗口客户区坐标转换为屏幕坐标
        '''
        x, y = pos
        x, y = map(int, (x, y))  # 确保坐标是整数
        pos = (x, y)
        return win32gui.ClientToScreen(hwnd, pos)

    def screen_to_client(self, hwnd: int, pos: Tuple[int, int]):
        '''
        将屏幕坐标转换为窗口客户区坐标
        '''
        x, y = pos
        x, y = map(int, (x, y))  # 确保坐标是整数
        pos = (x, y)
        return win32gui.ScreenToClient(hwnd, pos)
    
    def rect_client_to_screen(self, hwnd: int, rect: Tuple[int, int, int, int]) -> Tuple[int, int, int, int]:
        '''
        将窗口客户区矩形区域转换为屏幕坐标
        :param hwnd: 窗口句柄
        :param rect: 客户区矩形区域 (left, top, right, bottom)
        :return: 转换后的屏幕坐标 (left, top, right, bottom)
        '''
        left, top, right, bottom = rect
        # 获取窗口客户区左上角的屏幕坐标
        ltps = self.client_to_screen(hwnd, (left, top))
        # 获取窗口客户区右下角的屏幕坐标
        rbps = self.client_to_screen(hwnd, (right, bottom))
        return ltps[0], ltps[1], rbps[0], rbps[1]
    
    def rect_screen_to_client(self, hwnd: int, rect: Tuple[int, int, int, int]) -> Tuple[int, int, int, int]:
        '''
        将屏幕坐标矩形区域转换为窗口客户区坐标
        :param hwnd: 窗口句柄
        :param rect: 屏幕坐标矩形区域 (left, top, right, bottom)
        :return: 转换后的客户区坐标 (left, top, right, bottom)
        '''
        left, top, right, bottom = rect
        # 获取窗口客户区左上角的屏幕坐标
        ltps = self.screen_to_client(hwnd, (left, top))
        # 获取窗口客户区右下角的屏幕坐标
        rbps = self.screen_to_client(hwnd, (right, bottom))
        return ltps[0], ltps[1], rbps[0], rbps[1]
    
    def wait_key(self, hwnd: int, key: str) -> bool:
        '''
        检测指定按键是否被按下
        :param hwnd: 窗口句柄
        :param key: 要检测的按键，单个字符，如 'q', 'a' 等
        :return: 如果按键被按下返回 True，否则返回 False
        '''
        # 确保窗口是前台窗口
        if not self.is_window_foreground(hwnd):
            return False
        
        # 获取按键的虚拟键码
        vk_code = ord(key.upper())
        # 检测按键状态，最高位为1表示按键被按下
        key_state = win32api.GetAsyncKeyState(vk_code)
        return (key_state & 0x8000) != 0
    
    def get_mouse_pos(self, hwnd: int = None) -> Tuple[int, int]:
        '''
        获取鼠标相对于窗口客户区的坐标
        如果不指定hwnd，则返回鼠标的屏幕坐标
        '''
        screen_pos = win32api.GetCursorPos()
        if hwnd is None:
            return screen_pos
        return self.screen_to_client(hwnd, screen_pos)

    def send_message(self, hwnd: int, message: str):
        '''
        发送消息给窗口
        '''
        current_text = self.get_window_text(hwnd)
        self.append_window_text(hwnd, message)

    def get_window_text(self, hwnd: int) -> str:
        text_length = win32gui.SendMessage(hwnd, win32con.WM_GETTEXTLENGTH, 0, 0)
        buffer = ctypes.create_unicode_buffer(text_length + 1)
        win32gui.SendMessage(hwnd, win32con.WM_GETTEXT, text_length + 1, buffer)
        return buffer.value

    def append_window_text(self, hwnd: int, text: str):
        original = self.get_window_text(hwnd)
        new_text = original + text
        win32gui.SendMessage(hwnd, win32con.WM_SETTEXT, 0, new_text) 