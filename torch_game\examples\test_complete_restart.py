"""
测试完整游戏重启流程的脚本
验证优化后的游戏退出和重新开始逻辑
"""

import os
import sys
import time

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

print("🧪 测试完整游戏重启流程")
print("=" * 50)

def test_image_files():
    """测试所有需要的图片文件是否存在"""
    print("\n📋 检查图片文件")
    
    required_images = [
        r"models\sanxiao\放弃复活.png",
        r"models\sanxiao\放弃复活确定.png", 
        r"models\sanxiao\点击空白区域关闭.png",
        r"models\sanxiao\前往绘画.png",
        r"models\sanxiao\游戏失败.png",
        r"models\sanxiao\点击关闭屏幕.png"
    ]
    
    all_exist = True
    for image_file in required_images:
        if os.path.exists(image_file):
            print(f"✅ {image_file} 存在")
        else:
            print(f"❌ {image_file} 不存在")
            all_exist = False
    
    return all_exist


def test_game_assistant_methods():
    """测试游戏助手的新方法"""
    print("\n📋 测试游戏助手新方法")
    
    try:
        from s4_三消 import TripleEliminationGameAssistant
        from assist import AssistBasicToolkit
        
        # 创建游戏助手
        assist = AssistBasicToolkit()
        hwnd = assist.register_and_bind_window_objects("最强祖师")
        assist.detection.load_model(r"models\sanxiao\best.pt")
        
        game_assistant = TripleEliminationGameAssistant.created(hwnd, assist)
        print("✅ 游戏助手创建成功")
        
        # 检查新添加的图片是否加载
        required_attrs = [
            'game_fail_image',
            'game_fail_confirm_image', 
            'close_blank_area_image',
            'start_game_image'
        ]
        
        for attr in required_attrs:
            if hasattr(game_assistant, attr):
                print(f"✅ {attr} 已加载")
            else:
                print(f"❌ {attr} 未加载")
                return False
        
        # 测试游戏准备状态检查
        print("🔍 测试游戏准备状态检查...")
        is_ready = game_assistant.is_game_ready_for_play()
        print(f"游戏准备状态: {is_ready}")
        
        # 测试按钮点击方法（不实际点击，只是检查方法存在）
        if hasattr(game_assistant, '_click_button_with_retry'):
            print("✅ _click_button_with_retry 方法存在")
        else:
            print("❌ _click_button_with_retry 方法不存在")
            return False
        
        if hasattr(game_assistant, '_handle_complete_game_restart'):
            print("✅ _handle_complete_game_restart 方法存在")
        else:
            print("❌ _handle_complete_game_restart 方法不存在")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_click_match3_env_integration():
    """测试ClickMatch3Env的集成"""
    print("\n📋 测试ClickMatch3Env集成")
    
    try:
        from torch_game.core.env import ClickMatch3Env
        from s4_三消 import TripleEliminationGameAssistant
        from assist import AssistBasicToolkit
        
        # 创建游戏助手
        assist = AssistBasicToolkit()
        hwnd = assist.register_and_bind_window_objects("最强祖师")
        assist.detection.load_model(r"models\sanxiao\best.pt")
        game_assistant = TripleEliminationGameAssistant.created(hwnd, assist)
        
        # 创建环境
        env = ClickMatch3Env(
            game_assistant=game_assistant,
            max_steps=5,
            n_colors=6,
            storage_capacity=7
        )
        print("✅ 环境创建成功")
        
        # 检查新的属性
        if hasattr(env, '_need_game_restart'):
            print("✅ _need_game_restart 属性存在")
        else:
            print("❌ _need_game_restart 属性不存在")
            return False
        
        # 检查新的方法
        if hasattr(env, '_wait_for_game_restart'):
            print("✅ _wait_for_game_restart 方法存在")
        else:
            print("❌ _wait_for_game_restart 方法不存在")
            return False
        
        # 测试正常重置
        print("🔄 测试正常重置...")
        observation = env.reset()
        print(f"✅ 正常重置成功，观察值形状: {observation.shape}")
        
        # 关闭环境
        env.close()
        print("✅ 环境关闭成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def simulate_game_restart_flow():
    """模拟游戏重启流程（仅检测，不实际点击）"""
    print("\n📋 模拟游戏重启流程检测")
    
    try:
        from s4_三消 import TripleEliminationGameAssistant
        from assist import AssistBasicToolkit
        
        # 创建游戏助手
        assist = AssistBasicToolkit()
        hwnd = assist.register_and_bind_window_objects("最强祖师")
        assist.detection.load_model(r"models\sanxiao\best.pt")
        game_assistant = TripleEliminationGameAssistant.created(hwnd, assist)
        
        # 获取当前截图
        current_img = game_assistant.screenshot.capture_client_area()
        if current_img is None:
            print("❌ 无法获取游戏截图")
            return False
        
        print(f"✅ 获取游戏截图成功: {current_img.size}")
        
        # 检测各种按钮（不点击）
        buttons_to_check = [
            (game_assistant.game_fail_image, "放弃复活"),
            (game_assistant.game_fail_confirm_image, "放弃复活确定"),
            (game_assistant.close_blank_area_image, "点击空白区域关闭"),
            (game_assistant.start_game_image, "前往绘画")
        ]
        
        for button_image, button_name in buttons_to_check:
            result = game_assistant.image_search.find_image(current_img, button_image)
            if result:
                print(f"🔍 检测到 {button_name} 按钮: {result}")
            else:
                print(f"🔍 未检测到 {button_name} 按钮")
        
        # 检查游戏准备状态
        is_ready = game_assistant.is_game_ready_for_play()
        print(f"🎮 游戏准备状态: {is_ready}")
        
        return True
        
    except Exception as e:
        print(f"❌ 模拟测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("🚀 开始测试完整游戏重启流程")
    print("\n⚠️  请确保:")
    print("   1. '最强祖师'游戏正在运行")
    print("   2. 游戏处于可操作状态")
    print("   3. 本测试只进行检测，不会实际点击按钮")
    
    input("\n按Enter键继续...")
    
    # 测试1: 图片文件检查
    print("\n" + "="*50)
    if not test_image_files():
        print("❌ 图片文件检查失败，无法继续测试")
        return
    
    # 测试2: 游戏助手方法检查
    print("\n" + "="*50)
    if not test_game_assistant_methods():
        print("❌ 游戏助手方法检查失败")
        return
    
    # 测试3: 环境集成检查
    print("\n" + "="*50)
    if not test_click_match3_env_integration():
        print("❌ 环境集成检查失败")
        return
    
    # 测试4: 模拟游戏重启流程
    print("\n" + "="*50)
    if not simulate_game_restart_flow():
        print("❌ 模拟游戏重启流程失败")
        return
    
    print("\n" + "="*60)
    print("🎉 所有测试通过！")
    print("✅ 完整游戏重启流程优化验证成功")
    print("\n💡 优化内容:")
    print("   1. 完整的4步退出流程：放弃复活 → 确定 → 关闭 → 前往绘画")
    print("   2. 带重试机制的按钮点击方法")
    print("   3. 游戏准备状态检查方法")
    print("   4. 改进的等待游戏重启逻辑")
    print("   5. 适当的延时控制（2-5秒等待游戏加载）")
    print("\n🚀 现在可以运行完整的训练:")
    print("   python torch_game\\examples\\train_real_game_fixed.py --game_window \"最强祖师\" --model_path \"models\\sanxiao\\best.pt\"")


if __name__ == "__main__":
    main()
