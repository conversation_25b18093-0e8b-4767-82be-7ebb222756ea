"""
使用优化环境训练三消游戏智能体
集成了s4_三消.py的优化状态管理逻辑
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import torch
import numpy as np
from torch_game.core.env import OptimizedMatch3Env
from torch_game.core.agents import PPOAgent
from torch_game.core.train import Trainer
from torch_game.core.utils import setup_logging, save_config

# 添加s4_三消.py的路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from s4_三消 import TripleEliminationGameAssistant
from assist import AssistBasicToolkit


def create_game_assistant():
    """创建游戏助手实例"""
    try:
        print("正在初始化游戏助手...")
        assist = AssistBasicToolkit()
        
        # 注册并绑定窗口操作对象
        hwnd = assist.register_and_bind_window_objects('最强祖师')
        print(f"找到游戏窗口，句柄: {hwnd}")
        
        # 加载YOLO模型
        model_path = r"models\sanxiao\best.pt"
        if not os.path.exists(model_path):
            raise FileNotFoundError(f"模型文件不存在: {model_path}")
        
        assist.detection.load_model(model_path)
        print(f"YOLO模型加载成功: {model_path}")
        
        # 创建游戏助手
        assistant = TripleEliminationGameAssistant.created(hwnd, assist)
        print("游戏助手创建成功")
        
        return assistant
        
    except Exception as e:
        print(f"创建游戏助手失败: {e}")
        return None


def main():
    """主训练函数"""
    # 设置日志
    setup_logging()
    
    # 训练配置
    config = {
        'env': {
            'max_steps': 150,
            'n_colors': 19,
            'storage_capacity': 7
        },
        'agent': {
            'lr': 3e-4,
            'gamma': 0.99,
            'eps_clip': 0.2,
            'k_epochs': 4,
            'entropy_coef': 0.01,
            'value_coef': 0.5
        },
        'training': {
            'total_episodes': 1000,
            'save_interval': 50,
            'eval_interval': 25,
            'max_episode_length': 150
        }
    }
    
    # 保存配置
    save_config(config, 'optimized_match3_config.yaml')
    
    # 创建游戏助手
    game_assistant = create_game_assistant()
    if game_assistant is None:
        print("无法创建游戏助手，退出训练")
        return
    
    try:
        # 创建环境
        print("正在创建训练环境...")
        env = OptimizedMatch3Env(
            game_assistant=game_assistant,
            max_steps=config['env']['max_steps'],
            n_colors=config['env']['n_colors'],
            storage_capacity=config['env']['storage_capacity']
        )
        
        # 创建智能体
        print("正在创建PPO智能体...")
        agent = PPOAgent(
            state_dim=env.observation_space.shape[0],
            action_dim=env.action_space.n,
            lr=config['agent']['lr'],
            gamma=config['agent']['gamma'],
            eps_clip=config['agent']['eps_clip'],
            k_epochs=config['agent']['k_epochs'],
            entropy_coef=config['agent']['entropy_coef'],
            value_coef=config['agent']['value_coef']
        )
        
        # 创建训练器
        print("正在创建训练器...")
        trainer = Trainer(
            env=env,
            agent=agent,
            config=config['training']
        )
        
        # 开始训练
        print("开始训练...")
        print(f"环境观察空间: {env.observation_space.shape}")
        print(f"环境动作空间: {env.action_space.n}")
        print(f"总训练回合: {config['training']['total_episodes']}")
        print("-" * 50)
        
        trainer.train()
        
        print("训练完成！")
        
    except KeyboardInterrupt:
        print("\n训练被用户中断")
    except Exception as e:
        print(f"训练过程中出错: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 清理资源
        try:
            if 'env' in locals():
                env.close()
            if game_assistant is not None:
                if hasattr(game_assistant, 'assist') and hasattr(game_assistant.assist, 'close'):
                    game_assistant.assist.close()
        except Exception as e:
            print(f"清理资源时出错: {e}")


if __name__ == "__main__":
    main()
