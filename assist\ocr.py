"""
文字识别相关接口封装
"""
from wechatocr import engine
from typing import Union, Any, Dict
import numpy as np
from PIL import Image

class OCR:
    def __init__(self):
        self.ocr = engine()

    @property
    def result_formatter(self):
        """
        识别结果格式化器，用于格式化识别结果
        :return: 识别结果格式化器
        """
        return self.ocr.sync_ocr.result_formatter

    def recognize(self, img_input: Union[str, np.ndarray, Image.Image]) -> Dict[str, Any]:
        """
        对图片进行文字识别，返回原始识别结果
        :param img_input: 图片路径、OpenCV图像或PIL.Image
        :return: OCR识别结果字典
        """
        return self.ocr.sync_ocr.recognize(img_input)

    def recognize_text(self, img_input: Union[str, np.ndarray, Image.Image]) -> list:
        """
        直接返回识别到的文本列表
        """
        raw_result = self.recognize(img_input)
        return self.ocr.sync_ocr.get_texts(raw_result)

    def recognize_single_line(self, img_input: Union[str, np.ndarray, Image.Image]) -> str:
        """
        直接返回识别到的文本字符串
        """
        raw_result = self.recognize(img_input)
        return self.result_formatter.format_single_line(raw_result)

    def find_text_in_image(self, img_input: Union[str, np.ndarray, Image.Image], target_text: str, fuzzy: bool = False, use_regex: bool = False, estimate_inner_bbox: bool = True) -> list:
        """
        在图片中查找指定文字，支持多次出现、模糊匹配、正则查找和行内目标文字包围框估算。
        返回所有匹配文字的坐标信息列表。
        每个元素为dict，包含text、location（行bbox）、target_bbox、target_text。
        """
        result = self.recognize(img_input)
        return self.result_formatter.find_text_bbox(
            result, 
            target_text, 
            estimate_inner_bbox=estimate_inner_bbox, 
            use_regex=use_regex, 
            fuzzy=fuzzy
        )

    def find_char_positions(self, img_input: Union[str, np.ndarray, Image.Image]) -> list:
        """
        返回图片中每个字的位置信息
        :param img_input: 图片路径、OpenCV图像或PIL.Image
        :return: List[Dict]，每项包含char（字符）和bbox（边界框坐标）
        """
        result = self.recognize(img_input)
        return self.result_formatter.format_char_bbox(result)
