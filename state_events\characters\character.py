import re
from .scheduler import Scheduler
from .progress import Progress

import logging
logger = logging.getLogger(__name__)

class Character:
    '''
    角色管理器
    '''
    
    def __init__(self):
        self.hwnd = 0
        self.name = ''
        self.cls_name = ''
        self.progress = Progress()
        self.scheduler = Scheduler()

    @classmethod
    def bind_character(cls, hwnd, cls_name, win_title) -> 'Character':
        instance = cls()

        character_info = instance.find_character_info(win_title)
        if character_info:
            service_id, service_name, character_id, character_name = character_info
        else:
            character_name = win_title

        instance.hwnd = hwnd
        instance.cls_name = cls_name
        instance.name = character_name
        return instance

    def find_character_info(self, win_title, is_visible: bool=True):
        '''从win_title中获取游戏服务器和角色信息'''
        if not win_title: 
            return win_title

        pattern = ".*?(\d+) - (.*?) - (.*?)（ID：(\d+)）"

        character_info = re.match(pattern, win_title).groups()

        service_id, service_name, character_name, character_id = character_info

        return service_id, service_name, character_id, character_name

    @property
    def status(self) -> str:
        """获取角色当前状态"""
        return self.scheduler.get_character_state()

    @property
    def reason(self) -> str:
        """获取角色当前状态原因"""
        return self.scheduler.get_character_reason()

    @property
    def task_name(self):
        """获取角色当前任务"""
        return self.scheduler.get_current_tasker()

    def update_character_state(self, tasker):
        """更新角色当前状态"""

        # 更新任务进度
        self.progress.update_progress(tasker)
        # 更新角色调度状态
        self.scheduler.update_state(tasker)

    def get_progress_summary(self) -> dict:
        """获取进度摘要"""
        return self.progress.get_progress_summary() if self.progress else {}

    def is_all_finished(self) -> bool:
        """检查是否所有任务都已完成"""
        return self.progress.is_all_finished() if self.progress else False

    def add_taskers(self, task_names):
        """添加任务"""

        # 记录任务进度
        self.progress.add_taskers(task_names)
        # 添加任务到调度器
        self.scheduler.add_taskers(task_names)

    def __repr__(self) -> str:
        return f"Character({id(self)})"
        