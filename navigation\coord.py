'''
坐标转换器
'''

from typing import Tuple

class Coord:  # 坐标类

    def __init__(self, x: int = 0, y: int = 0):
        self.x = x
        self.y = y

    def __add__(self, other: 'Coord') -> 'Coord':
        return Coord(self.x + other.x, self.y + other.y)
    
    def __sub__(self, other: 'Coord') -> 'Coord':
        return Coord(self.x - other.x, self.y - other.y)
    
    def __mul__(self, factor: 'Coord') -> 'Coord':
        return Coord(self.x * factor.x, self.y * factor.y)
    
    def __truediv__(self, factor: 'Coord') -> 'Coord':
        return Coord(self.x / factor.x, self.y / factor.y)
    
    def truncate(self, n=0) -> 'Coord':
        mult_number = 10 ** n
        if n == 0:
            return Coord(int(self.x), int(self.y))
        
        return Coord(int(self.x * mult_number) / mult_number, int(self.y * mult_number) / mult_number)

    def round(self, n=0) -> 'Coord':
        if n == 0:
            return Coord(round(self.x), round(self.y))

        return Coord(round(self.x, n), round(self.y, n))
    
    def scale(self, factor: 'Coord') -> 'Coord':
        """缩放坐标：factor缩放比例：x,y轴分别缩放比例"""
        return Coord(self.x * factor.x, self.y * factor.y)
    
    def clamp(self, min_val: 'Coord', max_val: 'Coord') -> 'Coord':
        """将坐标限制在指定范围内"""
        return Coord(
            max(min_val.x, min(max_val.x, self.x)) + min_val.x,
            max(min_val.y, min(max_val.y, self.y)) + min_val.y
        )

    def __repr__(self) -> str:
        return f"Coord({self.x}, {self.y})"

    def tocoord(self) -> Tuple[int, int]:
        return (self.x, self.y)

if __name__ == "__main__":
    coord1 = Coord(1, 2)
    coord2 = Coord(3, 4)
    coord3 = coord1 + coord2
    print(type(coord3))