"""
微信OCR识别模块

该模块提供了对微信OCR功能的封装，支持三种不同的调用方式：
1. 同步识别方式调用（WeChatSyncOCR）- 简单场景
2. 高效任务方式调用（WeChatTasksOCR）- 批量处理场景
3. 高性能异步方式调用（WeChatAsyncOCR）- 高并发场景

每种调用方式都针对特定场景进行了优化，用户可以根据需求选择合适的调用方式。

使用示例：
```python
# 同步方式
from wechatocr import WeChatSyncOCR
ocr = WeChatSyncOCR()
result = ocr.recognize("image.jpg")
texts = ocr.get_texts(result)
ocr.close()

# 任务方式
from wechatocr import WeChatTasksOCR
ocr = WeChatTasksOCR()
task_id = ocr.submit_task("image.jpg")
result = ocr.get_result(task_id)
texts = ocr.get_texts(result)
ocr.close()

# 异步方式
import asyncio
from wechatocr import WeChatAsyncOCR
async def main():
    ocr = WeChatAsyncOCR()
    result = await ocr.recognize("image.jpg")
    texts = ocr.get_texts(result)
    ocr.close()
asyncio.run(main())
```

向后兼容：
为了兼容旧版本代码，提供了WeChatOCR类，默认使用同步调用方式。
```python
from wechatocr import WeChatOCR
ocr = WeChatOCR()
result = ocr.recognize("image.jpg")
texts = ocr.format_result(result)
ocr.close()
```
"""

# 导出接口层类
from wechatocr.interface import (
    WeChatSyncOCR,
    WeChatTasksOCR,  # WechatOCR.exe限制，并不能同时开启多个实例，tasks方式和sync方式效率一样
    WeChatAsyncOCR,  # WechatOCR.exe限制，并不能同时开启多个实例，async方式和sync方式效率一样
    WeChatOCR,
    OCREngine,
)

# 引擎单例
class engine:

    def __init__(self):
        self.engine = OCREngine()
    
    @property
    def sync_ocr(self) -> WeChatSyncOCR:
        """创建同步OCR引擎"""
        return WeChatSyncOCR(self.engine)
    
    @property
    def tasks_ocr(self) -> WeChatTasksOCR:
        """创建任务OCR引擎"""
        return WeChatTasksOCR(self.engine)
    
    @property
    def async_ocr(self) -> WeChatAsyncOCR:
        """创建异步OCR引擎"""
        return WeChatAsyncOCR(self.engine)
    
    @property
    def ocr(self) -> WeChatOCR:
        """创建默认OCR引擎"""
        return WeChatOCR(self.engine)
    
    def __del__(self):
        """析构时关闭OCR引擎"""
        if hasattr(self, "engine"):
            try:
                self.engine.close()
            except Exception as e:
                print(f"关闭OCR引擎时出错: {e}")
        

# 版本信息
__version__ = "1.0.0"
__author__ = "中南"
__description__ = "微信OCR识别模块，支持同步、任务和异步三种调用方式"
