"""
训练智能体玩真实点击式三消游戏的示例脚本
使用ClickMatch3Env环境和s4_三消.py的游戏助手
"""

import os
import sys
import argparse
import numpy as np
import matplotlib.pyplot as plt

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from torch_game.core.env import ClickMatch3Env
from torch_game.core.agents import PPOAgent
from torch_game.core.utils import Trainer

# 导入游戏助手（需要确保路径正确）
try:
    from s4_三消 import TripleEliminationGameAssistant
    from assist import AssistBasicToolkit
except ImportError:
    print("警告: 无法导入游戏助手模块，将使用模拟模式")
    TripleEliminationGameAssistant = None
    AssistBasicToolkit = None


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='Train an agent to play real click-style Match-3 game')
    
    # 环境参数
    parser.add_argument('--max_steps', type=int, default=200, help='Maximum steps per episode')
    parser.add_argument('--n_colors', type=int, default=6, help='Number of block colors')
    parser.add_argument('--storage_capacity', type=int, default=7, help='Storage area capacity')
    
    # 智能体参数
    parser.add_argument('--hidden_dims', type=int, nargs='+', default=[512, 256], help='Hidden dimensions of the networks')
    parser.add_argument('--lr', type=float, default=1e-4, help='Learning rate')
    parser.add_argument('--gamma', type=float, default=0.995, help='Discount factor')
    parser.add_argument('--gae_lambda', type=float, default=0.97, help='GAE lambda parameter')
    parser.add_argument('--clip_epsilon', type=float, default=0.15, help='PPO clip epsilon')
    parser.add_argument('--entropy_coef', type=float, default=0.01, help='Entropy coefficient')
    parser.add_argument('--value_coef', type=float, default=0.5, help='Value coefficient')
    
    # 训练参数
    parser.add_argument('--num_episodes', type=int, default=1000, help='Number of training episodes')
    parser.add_argument('--buffer_capacity', type=int, default=10000, help='Capacity of replay buffer')
    parser.add_argument('--batch_size', type=int, default=128, help='Batch size for training')
    parser.add_argument('--update_frequency', type=int, default=8, help='Frequency of agent updates')
    parser.add_argument('--log_frequency', type=int, default=10, help='Frequency of logging')
    
    # 评估参数
    parser.add_argument('--eval_episodes', type=int, default=5, help='Number of evaluation episodes')
    parser.add_argument('--eval_frequency', type=int, default=50, help='Frequency of evaluation')
    
    # 保存/加载参数
    parser.add_argument('--save_dir', type=str, default='checkpoints/real_game', help='Directory to save checkpoints')
    parser.add_argument('--load_model', type=str, default=None, help='Path to load a pre-trained model')
    parser.add_argument('--eval_only', action='store_true', help='Only run evaluation, no training')
    
    # 游戏参数
    parser.add_argument('--game_window', type=str, default='最强祖师', help='Game window title')
    parser.add_argument('--model_path', type=str, default=r"models\sanxiao\best.pt", help='YOLO model path')
    parser.add_argument('--simulate_mode', action='store_true', help='Run in simulation mode without real game')
    
    # 可视化参数
    parser.add_argument('--plot_results', action='store_true', help='Plot training results')
    
    return parser.parse_args()


def create_game_assistant(args):
    """创建游戏助手"""
    if args.simulate_mode or TripleEliminationGameAssistant is None:
        print("运行在模拟模式")
        return None
    
    try:
        # 初始化游戏助手
        assist = AssistBasicToolkit()
        hwnd = assist.register_and_bind_window_objects(args.game_window)
        assist.detection.load_model(args.model_path)
        
        game_assistant = TripleEliminationGameAssistant.created(hwnd, assist)
        
        print(f"成功连接到游戏窗口: {args.game_window}")
        return game_assistant
        
    except Exception as e:
        print(f"创建游戏助手失败: {e}")
        print("切换到模拟模式")
        return None


def create_environment(args, game_assistant):
    """创建游戏环境"""
    env = ClickMatch3Env(
        game_assistant=game_assistant,
        max_steps=args.max_steps,
        n_colors=args.n_colors,
        storage_capacity=args.storage_capacity
    )
    
    print(f"环境创建成功:")
    print(f"  观察空间: {env.observation_space.shape}")
    print(f"  动作空间: {env.action_space.n}")
    print(f"  最大步数: {args.max_steps}")
    
    return env


def create_agent(args, env):
    """创建PPO智能体"""
    state_dim = env.observation_space.shape[0]
    action_dim = env.action_space.n
    
    agent = PPOAgent(
        state_dim=state_dim,
        action_dim=action_dim,
        lr=args.lr,
        gamma=args.gamma,
        epsilon=args.clip_epsilon,
        value_coef=args.value_coef,
        entropy_coef=args.entropy_coef,
        hidden_dim=args.hidden_dims[0] if args.hidden_dims else 256
    )
    
    print(f"智能体创建成功:")
    print(f"  状态维度: {state_dim}")
    print(f"  动作维度: {action_dim}")
    print(f"  网络结构: {args.hidden_dims}")
    
    return agent


def plot_training_results(results, save_path=None):
    """绘制训练结果"""
    if not results or 'episodes' not in results:
        print("没有训练结果可绘制")
        return
    
    episodes = results['episodes']
    rewards = [ep.get('reward', 0) for ep in episodes]
    lengths = [ep.get('length', 0) for ep in episodes]
    
    # 创建图形
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8), sharex=True)
    
    # 绘制奖励
    ax1.plot(rewards, 'b-', alpha=0.7, label='Episode Reward')
    if len(rewards) > 10:
        # 移动平均
        window = min(50, len(rewards) // 10)
        moving_avg = np.convolve(rewards, np.ones(window)/window, mode='valid')
        ax1.plot(range(window-1, len(rewards)), moving_avg, 'r-', linewidth=2, label=f'Moving Average ({window})')
    
    ax1.set_ylabel('Reward')
    ax1.set_title('Training Results - Real Game')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 绘制回合长度
    ax2.plot(lengths, 'g-', alpha=0.7, label='Episode Length')
    if len(lengths) > 10:
        window = min(50, len(lengths) // 10)
        moving_avg = np.convolve(lengths, np.ones(window)/window, mode='valid')
        ax2.plot(range(window-1, len(lengths)), moving_avg, 'orange', linewidth=2, label=f'Moving Average ({window})')
    
    ax2.set_xlabel('Episode')
    ax2.set_ylabel('Length')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存图形
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"训练结果图保存到: {save_path}")
    
    plt.show()


def evaluate_agent(env, agent, num_episodes=5):
    """评估智能体性能"""
    print(f"开始评估智能体 ({num_episodes} 回合)...")
    
    total_reward = 0
    total_length = 0
    success_episodes = 0
    
    for episode in range(num_episodes):
        state = env.reset()
        episode_reward = 0
        episode_length = 0
        done = False
        
        print(f"评估回合 {episode + 1}/{num_episodes}")
        
        while not done:
            # 使用确定性策略
            action = agent.select_action(state, deterministic=True)
            state, reward, done, info = env.step(action)
            episode_reward += reward
            episode_length += 1
            
            # 渲染环境状态
            if episode_length % 10 == 0:
                env.render()
        
        total_reward += episode_reward
        total_length += episode_length
        
        if episode_reward > 0:
            success_episodes += 1
        
        print(f"  回合奖励: {episode_reward:.2f}, 长度: {episode_length}")
    
    avg_reward = total_reward / num_episodes
    avg_length = total_length / num_episodes
    success_rate = success_episodes / num_episodes
    
    print(f"\n评估结果:")
    print(f"  平均奖励: {avg_reward:.2f}")
    print(f"  平均长度: {avg_length:.2f}")
    print(f"  成功率: {success_rate:.2%}")
    
    return {
        'avg_reward': avg_reward,
        'avg_length': avg_length,
        'success_rate': success_rate,
        'total_reward': total_reward
    }


def main():
    """主函数"""
    # 解析参数
    args = parse_args()
    
    # 创建保存目录
    os.makedirs(args.save_dir, exist_ok=True)
    
    print("=" * 60)
    print("强化学习训练真实点击式三消游戏")
    print("=" * 60)
    
    # 创建游戏助手
    game_assistant = create_game_assistant(args)
    
    # 创建环境
    env = create_environment(args, game_assistant)
    
    # 创建智能体
    agent = create_agent(args, env)
    
    # 加载预训练模型（如果指定）
    if args.load_model:
        print(f"加载模型: {args.load_model}")
        try:
            agent.load(args.load_model)
            print("模型加载成功")
        except Exception as e:
            print(f"模型加载失败: {e}")
    
    # 仅评估模式
    if args.eval_only:
        eval_results = evaluate_agent(env, agent, args.eval_episodes)
        return
    
    # 创建训练器
    print("\n开始训练...")
    
    # 简化的训练循环（这里需要实现完整的训练器）
    results = {'episodes': []}
    
    try:
        for episode in range(args.num_episodes):
            state = env.reset()
            episode_reward = 0
            episode_length = 0
            done = False
            
            while not done:
                action = agent.select_action(state)
                if isinstance(action, tuple):
                    action = action[0]  # 如果返回元组，取第一个元素
                
                state, reward, done, info = env.step(action)
                episode_reward += reward
                episode_length += 1
            
            results['episodes'].append({
                'reward': episode_reward,
                'length': episode_length
            })
            
            # 记录日志
            if episode % args.log_frequency == 0:
                print(f"Episode {episode}: Reward={episode_reward:.2f}, Length={episode_length}")
            
            # 定期评估
            if episode % args.eval_frequency == 0 and episode > 0:
                eval_results = evaluate_agent(env, agent, 3)
            
            # 保存模型
            if episode % 100 == 0 and episode > 0:
                model_path = os.path.join(args.save_dir, f'model_episode_{episode}.pth')
                agent.save(model_path)
                print(f"模型已保存: {model_path}")
    
    except KeyboardInterrupt:
        print("\n训练被用户中断")
    
    # 最终评估
    print("\n最终评估:")
    final_eval = evaluate_agent(env, agent, args.eval_episodes)
    
    # 保存最终模型
    final_model_path = os.path.join(args.save_dir, 'final_model.pth')
    agent.save(final_model_path)
    print(f"最终模型已保存: {final_model_path}")
    
    # 绘制训练结果
    if args.plot_results and results['episodes']:
        plot_path = os.path.join(args.save_dir, 'training_results.png')
        plot_training_results(results, plot_path)


if __name__ == "__main__":
    main()
