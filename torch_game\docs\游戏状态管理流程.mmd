
flowchart TD
    A[程序启动] --> B[ensure_game_ready]
    B --> C[detect_current_game_state]

    C --> D{状态检测}
    D -->|优先级检测| E[_check_image_state]
    E --> F{找到状态?}
    F -->|是| G[handle_game_state]
    F -->|否| H[检查游戏中状态]

    G --> I[查找状态配置]
    I --> J[_handle_state_action]
    J --> K[_click_button_with_retry]
    J --> L[wait_for_state_change]

    H -->|有方块| M[返回in_game]
    H -->|无方块| N[返回unknown]

    M --> O[开始方块检测循环]
    N --> P[短暂等待重试]
    K --> L
    L --> Q{检测到期望状态?}
    Q -->|是| R[状态切换成功]
    Q -->|否| S[超时或继续等待]

    R --> C
    S --> C
    P --> C

    O --> T[主游戏循环]
    T --> U[check_and_handle_game_state]
    U --> V{游戏状态变化?}
    V -->|是| G
    V -->|否| W[继续游戏]
    W --> T
