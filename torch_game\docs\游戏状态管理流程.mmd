
flowchart TD
    A[程序启动] --> B[ensure_game_ready]
    B --> C[detect_current_game_state]
    
    C --> D{状态检测}
    D -->|配置化检测| E[_check_image_state]
    E --> F{找到状态?}
    F -->|是| G[handle_game_state]
    F -->|否| H[检查游戏中状态]
    
    G --> I[查找状态配置]
    I --> J[_handle_state_action]
    J --> K[_click_button_with_retry]
    J --> L[等待指定时间]
    
    H -->|有方块| M[返回in_game]
    H -->|无方块| N[返回unknown]
    
    M --> O[开始方块检测循环]
    N --> P[等待1秒重试]
    K --> P
    L --> P
    P --> N
