'''方案二：动态帧同步（画面变化检测）'''

import cv2
import numpy as np
import pyautogui
import win32gui
import time
import mss
from ultralytics import YOLO
from time import perf_counter

class DynamicFrameSync:
    def __init__(self, hwnd, target_fps=30):
        self.hwnd = hwnd
        self.target_interval = 1 / target_fps
        self.last_frame = None
        self.change_threshold = 50  # 像素变化阈值
        self.min_wait = 0.005       # 5ms最小等待
        self.alpha = 0.3            # 移动平均系数

        # 初始化截图参数
        self.update_window_rect()
        
        # 加载YOLOv8模型
        self.model = YOLO('xy2.pt', 
                         device='cuda' if torch.cuda.is_available() else 'cpu',
                         verbose=False)
        
        # 初始化统计变量
        self.frame_count = 0
        self.total_delay = 0
        self.detection_count = 0

    def update_window_rect(self):
        """ 更新窗口位置和尺寸 """
        try:
            rect = win32gui.GetWindowRect(self.hwnd)
            self.left, self.top, self.right, self.bottom = rect
            self.width = self.right - self.left
            self.height = self.bottom - self.top
            self.monitor = {"left": self.left, "top": self.top,
                           "width": self.width, "height": self.height}
        except:
            raise RuntimeError("窗口句柄失效")

    def get_frame(self):
        """ 高性能截图并返回灰度图 """
        with mss.mss() as sct:
            frame = np.array(sct.grab(self.monitor))[:,:,:3]
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        return gray, frame

    def detect_changes(self, current_gray):
        """ 检测画面变化并返回变化程度 """
        if self.last_frame is None:
            return float('inf')
        
        # 计算帧间差异
        diff = cv2.absdiff(current_gray, self.last_frame)
        changed_pixels = np.sum(diff > 25)  # 阈值25的差异视为有效变化
        return changed_pixels

    def process_frame(self, color_frame):
        """ 执行YOLOv8检测并返回结果 """
        results = self.model.predict(
            color_frame,
            imgsz=320,
            conf=0.5,
            half=True,
            max_det=2,
            device=self.model.device
        )
        
        detections = []
        for result in results:
            for box in result.boxes:
                x1, y1, x2, y2 = map(int, box.xyxy[0].cpu().numpy())
                center_x = (x1 + x2) // 2 + self.left
                center_y = (y1 + y2) // 2 + self.top
                detections.append({
                    'center': (center_x, center_y),
                    'conf': box.conf.item(),
                    'box': (x1, y1, x2, y2)
                })
        return detections

    def run(self, target_pos, error_range, max_retry=20):
        """ 主控制循环 """
        target_x, target_y = target_pos
        error_x, error_y = error_range
        retry_count = 0
        last_update = time.time()

        while True:
            cycle_start = perf_counter()
            
            # 每5秒更新一次窗口位置
            if time.time() - last_update > 5:
                self.update_window_rect()
                last_update = time.time()

            # 获取当前帧
            current_gray, color_frame = self.get_frame()
            
            # 计算画面变化量
            change_level = self.detect_changes(current_gray)
            
            # 动态等待策略
            if change_level < self.change_threshold:
                wait_time = max(self.target_interval * 0.7, self.min_wait)
                time.sleep(wait_time)
                self.last_frame = current_gray
                continue

            # 执行目标检测
            detections = self.process_frame(color_frame)
            self.last_frame = current_gray
            
            if not detections:
                retry_count = min(retry_count + 1, max_retry)
                if retry_count > max_retry:
                    self.reset_mouse()
                    retry_count = 0
                continue
            else:
                retry_count = 0
                self.detection_count += 1

            # 选择最佳检测目标
            best_det = max(detections, key=lambda x: x['conf'])
            current_x, current_y = best_det['center']

            # 到达目标判断
            if (abs(target_x - current_x) < error_x and 
                abs(target_y - current_y) < error_y):
                break

            # 计算移动向量
            dx = int((target_x - current_x) * 0.45)  # 降低移动系数
            dy = int((target_y - current_y) * 0.45)
            
            # 执行鼠标移动
            if abs(dx) > 1 or abs(dy) > 1:
                pyautogui.moveRel(dx, dy, _pause=False)

            # 越界检测
            self.boundary_check()

            # 自适应延迟调整
            elapsed = perf_counter() - cycle_start
            self.total_delay += elapsed
            self.frame_count += 1
            
            # 动态调整阈值
            if self.frame_count % 50 == 0:
                avg_delay = self.total_delay / self.frame_count
                self.change_threshold = int(50 + (avg_delay * 1000 - 15) * 10)
                self.change_threshold = np.clip(self.change_threshold, 30, 200)

            # 维持帧率
            remaining = self.target_interval - elapsed
            if remaining > self.min_wait:
                time.sleep(remaining * 0.8)

    def boundary_check(self):
        """ 鼠标越界检测 """
        mouse_x, mouse_y = pyautogui.position()
        if not (self.left <= mouse_x <= self.right and 
                self.top <= mouse_y <= self.bottom):
            center_x = self.left + self.width // 2
            center_y = self.top + self.height // 2
            pyautogui.moveTo(center_x, center_y)

    def reset_mouse(self):
        """ 重置鼠标到窗口中心 """
        center_x = self.left + self.width // 2
        center_y = self.top + self.height // 2
        pyautogui.moveTo(center_x, center_y)
        print("执行鼠标重置")

    def print_stats(self):
        """ 打印性能统计 """
        if self.frame_count == 0:
            return
        avg_delay = self.total_delay / self.frame_count * 1000
        detect_rate = self.detection_count / self.frame_count * 100
        print(f"平均延迟: {avg_delay:.1f}ms | 检测率: {detect_rate:.1f}% | 当前阈值: {self.change_threshold}")

'''
关键参数调节建议
1、画面变化灵敏度​​
# 调整这些参数：
self.change_threshold = 50       # 初始阈值
diff > 25                        # 像素差异判定阈值
self.alpha = 0.3                 # 阈值调整速度

# 调试时可添加可视化：
cv2.imshow('Difference', diff)
cv2.waitKey(1)

2、移动平滑度控制​​
# 修改移动系数（0.3-0.5之间）
dx = int((target_x - current_x) * 0.45)
# 添加移动速度限制
dx = np.clip(dx, -15, 15)  # 限制单次最大移动15像素

3、帧率稳定性​​
# 调整目标帧率（30-60之间）
self.target_interval = 1 / 30  # 30fps
# 动态调整等待时间
wait_time = max(self.target_interval * 0.7, self.min_wait)
time.sleep(wait_time)

4、稳定性优化​​
# 异常恢复机制​​
# 重试机制参数
max_retry=20                  # 最大失败次数
self.change_threshold         # 自动降低阈值恢复检测

# 窗口位置更新间隔（秒）
if time.time() - last_update > 5:
    self.update_window_rect()

5、可视化调试
# 在process_frame()方法中添加调试绘制
def process_frame(self, color_frame):
    # ...原有检测逻辑...
    if debug:
        for det in detections:
            x1, y1, x2, y2 = det['box']
            cv2.rectangle(color_frame, (x1,y1), (x2,y2), (0,255,0), 2)
        cv2.imshow('Detection', color_frame)
        cv2.waitKey(1)
'''

# 使用示例
if __name__ == "__main__":
    # 获取窗口句柄
    hwnd = win32gui.FindWindow(None, "Your Game Window Title")
    if hwnd == 0:
        raise Exception("未找到游戏窗口")

    # 初始化控制器
    sync_controller = DynamicFrameSync(hwnd, target_fps=30)
    
    try:
        # 目标坐标(400,300)，误差范围±5px
        sync_controller.run(
            target_pos=(400, 300),
            error_range=(5, 5)
        )
    finally:
        sync_controller.print_stats()