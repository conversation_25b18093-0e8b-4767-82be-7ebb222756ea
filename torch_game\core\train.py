"""
训练脚本
用于训练强化学习智能体
"""

import argparse
import os
import torch
import numpy as np
import random

from torch_game.core.env import OptimizedMatch3Env
from torch_game.core.agents import PPOAgent
from torch_game.core.utils import ConfigManager

def set_seed(seed):
    """设置随机种子"""
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    np.random.seed(seed)
    random.seed(seed)
    torch.backends.cudnn.deterministic = True

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='训练Match-3游戏智能体')
    parser.add_argument('--config', type=str, default=None, help='配置文件路径')
    parser.add_argument('--seed', type=int, default=42, help='随机种子')
    parser.add_argument('--device', type=str, default='cuda' if torch.cuda.is_available() else 'cpu', 
                        help='训练设备')
    parser.add_argument('--resume', type=str, default=None, help='恢复训练的模型路径')
    args = parser.parse_args()
    
    # 设置随机种子
    set_seed(args.seed)
    
    # 加载配置
    if args.config:
        config = ConfigManager.load(args.config)
    else:
        # 使用默认配置
        config = {
            'env': {'max_steps': 150, 'n_colors': 19, 'storage_capacity': 7},
            'agent': {'lr': 3e-4, 'gamma': 0.99, 'eps_clip': 0.2},
            'training': {'total_episodes': 1000}
        }

    # 创建环境
    env_config = config.get('env', {})
    env = OptimizedMatch3Env(
        game_assistant=None,  # 需要在实际使用时提供
        max_steps=env_config.get('max_steps', 150),
        n_colors=env_config.get('n_colors', 19),
        storage_capacity=env_config.get('storage_capacity', 7)
    )
    
    # 创建智能体
    agent_config = config.get('agent', {})
    state_dim = np.prod(env.observation_space.shape)
    action_dim = env.action_space.n
    
    agent = PPOAgent(
        state_dim=state_dim,
        action_dim=action_dim,
        lr=agent_config.get('lr', 3e-4),
        gamma=agent_config.get('gamma', 0.99),
        epsilon=agent_config.get('epsilon', 0.2),
        value_coef=agent_config.get('value_coef', 0.5),
        entropy_coef=agent_config.get('entropy_coef', 0.01),
        hidden_dim=agent_config.get('hidden_dim', 256),
        device=args.device
    )
    
    # 如果指定了恢复训练，加载模型
    if args.resume:
        print(f"从 {args.resume} 恢复训练")
        agent.load(args.resume)
    
    # TODO: 创建训练器 (待实现完整的Trainer类)
    # trainer = Trainer(agent, env, config)
    # trainer.train()

    print("训练脚本已更新，等待Trainer类实现...")
    print(f"环境: {env}")
    print(f"智能体: {agent}")
    print(f"配置: {config}")
    
    # 关闭环境
    env.close()

if __name__ == '__main__':
    main()