from ..state import State
from ..handlers import Handlers
from .tasker import Tasker

from assist import AssistBasicToolkit

from event_bus import event_bus

import logging
logger = logging.getLogger(__name__)

class Taskers:
    '''
    任务处理器
    '''

    def __init__(self):
        self.hwnd = 0
        self.assist: AssistBasicToolkit = None
        self.hwnd_taskers = {}  # hwnd -> tasker

        event_bus.subscribe('on_tasker_started', self._on_tasker_started)
        event_bus.subscribe('on_tasker_executed', self._on_tasker_executed)

    @classmethod
    def created(cls, hwnd, assist: AssistBasicToolkit) -> 'Taskers':
        self = cls()
        self.hwnd = hwnd
        self.assist = assist
        return self
    
    def _on_tasker_started(self, hwnd, name):
        """任务处理器开始事件回调，主要设置待执行的任务"""
        if hwnd not in self.hwnd_taskers:
            handler = self.get_task_handler_by_name(name)
            tasker = Tasker.bind_tasker(hwnd, name, handler)
            tasker.register_assist_objects(self.hwnd, self.assist)
            self.register_tasker(hwnd, tasker)

        # 获取当前任务处理器
        tasker = self._get_current_tasker(hwnd)

        # 如果当前任务不等于目标任务，重新设置当前任务为目标任务
        if name != tasker.name:
            # 重新创建正确的tasker
            handler = self.get_task_handler_by_name(name)
            tasker = Tasker.bind_tasker(hwnd, name, handler)
            tasker.register_assist_objects(self.hwnd, self.assist)
            self.register_tasker(hwnd, tasker)

        # print(f"[Taskers] 任务处理器已设置，当前任务: {tasker.name}")

    def _on_tasker_executed(self, hwnd, name):
        """任务处理器执行事件回调"""
        tasker = self._get_current_tasker(hwnd)
        if not tasker:
            logger.error(f"[Taskers] 未找到任务处理器: {name} ({hwnd})")
            return

        # 执行任务
        tasker._on_tasker_executed()

    def register_tasker(self, hwnd, tasker: Tasker):
        """注册任务处理器"""
        if hwnd not in self.hwnd_taskers:
            self._set_hwnd_tasker(hwnd, tasker)
            return

        # 替换tasker info
        self._set_hwnd_tasker(hwnd, tasker)

    def _set_hwnd_tasker(self, hwnd, tasker: Tasker):
        """设置任务处理器"""

        old_tasker = self._get_current_tasker(hwnd)
        if not old_tasker:
            self.hwnd_taskers[hwnd] = tasker
            return

        # 判断旧任务是否已完成或失败
        if not State.is_completed(old_tasker.status):
            return

        # 旧任务已完成或失败，替换为新任务
        self.hwnd_taskers[hwnd] = tasker

    def get_task_handler_by_name(self, name):
        """根据任务名称从task_handlers中获取任务执行器"""
        task_handler = getattr(Handlers, name, None)
        if not task_handler:
            # logger.error(f"TaskHandlers {name} not found")
            return
        return task_handler

    def _get_current_tasker(self, hwnd) -> Tasker:
        """获取当前任务处理器"""
        return self.hwnd_taskers.get(hwnd)

