"""
微信OCR使用示例

本模块展示了三种不同调用方式的使用示例：
1. 同步识别方式调用 - 简单场景
2. 高效任务方式调用 - 批量处理场景
3. 高性能异步方式调用 - 高并发场景

每种调用方式都针对特定场景进行了优化，用户可以根据需求选择合适的调用方式。
"""

import os
import time
import asyncio
import cv2
import numpy as np
from PIL import Image

from wechatocr.interface import WeChatSyncOCR, WeChatTasksOCR, WeChatAsyncOCR, OCREngine


def example_sync(engine: OCREngine = None):
    """同步识别方式示例"""
    print("\n===== 同步识别方式示例 =====")
    
    # 初始化
    ocr = WeChatSyncOCR(engine)
    
    try:
        # 示例1：通过路径识别
        image_path = "test_image.jpg"  # 替换为实际图片路径
        if os.path.exists(image_path):
            start_time = time.time()
            result1 = ocr.recognize(image_path)
            texts1 = ocr.get_texts(result1)
            print(f"路径识别结果: {texts1}")
            print(f"耗时: {time.time() - start_time:.2f}秒")
        
        # 示例2：通过OpenCV图像识别
        if os.path.exists(image_path):
            cv_img = cv2.imread(image_path)
            start_time = time.time()
            result2 = ocr.recognize(cv_img)
            texts2 = ocr.get_texts(result2)
            print(f"OpenCV图像识别结果: {texts2}")
            print(f"耗时: {time.time() - start_time:.2f}秒")
        
        # 示例3：通过PIL图像识别
        if os.path.exists(image_path):
            pil_img = Image.open(image_path)
            start_time = time.time()
            result3 = ocr.recognize(pil_img)
            texts3 = ocr.get_texts(result3)
            print(f"PIL图像识别结果: {texts3}")
            print(f"耗时: {time.time() - start_time:.2f}秒")
        
        # 示例4：识别指定区域
        if os.path.exists(image_path):
            roi = (0, 0, 200, 100)  # 左上右下坐标
            start_time = time.time()
            result4 = ocr.recognize_roi(image_path, roi)
            texts4 = ocr.get_texts(result4)
            print(f"区域识别结果: {texts4}")
            print(f"耗时: {time.time() - start_time:.2f}秒")
        
        # 示例5：批量识别
        if os.path.exists(image_path):
            start_time = time.time()
            results = ocr.batch_recognize([image_path, image_path])
            for i, result in enumerate(results):
                texts = ocr.get_texts(result)
                print(f"批量识别结果 {i+1}: {texts}")
            print(f"总耗时: {time.time() - start_time:.2f}秒")
    
    finally:
        # 关闭OCR引擎
        ocr.close()


def example_task(engine: OCREngine = None):
    """高效任务方式示例"""
    print("\n===== 高效任务方式示例 =====")
    
    # 初始化
    ocr = WeChatTasksOCR(engine)
    
    try:
        image_path = "test_image.jpg"  # 替换为实际图片路径
        
        if os.path.exists(image_path):
            # 示例1：同步识别
            start_time = time.time()
            result1 = ocr.recognize(image_path)
            texts1 = ocr.get_texts(result1)
            print(f"同步识别结果: {texts1}")
            print(f"耗时: {time.time() - start_time:.2f}秒")
            
            # 示例2：异步提交任务
            start_time = time.time()
            task_id = ocr.submit_task(image_path)
            print(f"任务已提交，ID: {task_id}")
            
            # 获取任务结果
            result2 = ocr.get_result(task_id)
            texts2 = ocr.get_texts(result2)
            print(f"异步任务结果: {texts2}")
            print(f"总耗时: {time.time() - start_time:.2f}秒")
            
            # 示例3：批量识别
            start_time = time.time()
            
            # 创建多个任务
            task_ids = []
            for i in range(5):  # 提交5个任务
                task_id = ocr.submit_task(image_path)
                task_ids.append(task_id)
                print(f"任务 {i+1} 已提交，ID: {task_id}")
            
            # 获取所有任务结果
            for i, task_id in enumerate(task_ids):
                result = ocr.get_result(task_id)
                texts = ocr.get_texts(result)
                print(f"任务 {i+1} 结果: {texts}")
            
            print(f"批量任务总耗时: {time.time() - start_time:.2f}秒")
            
            # 示例4：使用batch_recognize方法
            start_time = time.time()
            results = ocr.batch_recognize([image_path] * 5)  # 识别5次相同图片
            print(f"批量识别完成，共 {len(results)} 个结果")
            print(f"批量识别总耗时: {time.time() - start_time:.2f}秒")
    
    finally:
        # 关闭OCR引擎
        ocr.close()


async def example_async(engine: OCREngine = None):
    """高性能异步方式示例"""
    print("\n===== 高性能异步方式示例 =====")
    
    # 初始化
    ocr = WeChatAsyncOCR(engine)
    
    try:
        image_path = "test_image.jpg"  # 替换为实际图片路径
        
        if os.path.exists(image_path):
            # 示例1：异步识别单张图片
            start_time = time.time()
            result1 = await ocr.recognize(image_path)
            texts1 = ocr.get_texts(result1)
            print(f"异步识别结果: {texts1}")
            print(f"耗时: {time.time() - start_time:.2f}秒")
            
            # 示例2：异步批量识别
            start_time = time.time()
            results = await ocr.batch_recognize([image_path] * 5)  # 识别5次相同图片
            print(f"异步批量识别完成，共 {len(results)} 个结果")
            for i, result in enumerate(results):
                texts = ocr.get_texts(result)
                print(f"批量结果 {i+1}: {texts}")
            print(f"异步批量识别总耗时: {time.time() - start_time:.2f}秒")
            
            # 示例3：并发处理多个图片
            start_time = time.time()
            
            # 创建多个任务
            tasks = []
            for i in range(10):  # 创建10个并发任务
                task = ocr.recognize(image_path)
                tasks.append(task)
            
            # 并发执行所有任务
            results = await asyncio.gather(*tasks)
            print(f"并发处理完成，共 {len(results)} 个结果")
            print(f"并发处理总耗时: {time.time() - start_time:.2f}秒")
    
    finally:
        # 关闭OCR引擎
        ocr.close()


def run_all_examples():
    """运行所有示例"""
    print("\n===== 运行所有示例 =====")
    engine = OCREngine()
    # 运行同步示例
    example_sync(engine)
    
    # 运行任务示例
    example_task(engine)
    
    # 运行异步示例
    asyncio.run(example_async(engine))


if __name__ == "__main__":
    # 替换为实际图片路径
    test_image_path = "test_image.jpg"
    
    # 如果测试图片不存在，创建一个简单的测试图片
    if not os.path.exists(test_image_path):
        print(f"创建测试图片: {test_image_path}")
        # 创建一个带有文字的图片
        img = np.zeros((200, 400, 3), dtype=np.uint8)
        img.fill(255)  # 白色背景
        cv2.putText(img, "OCR Test Image", (50, 100), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
        cv2.imwrite(test_image_path, img)
    
    # 运行所有示例
    run_all_examples()
