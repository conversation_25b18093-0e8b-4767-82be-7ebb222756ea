



import time

from ..state import State
from ..taskers import Tasker

class Progress:
    '''
    任务进度管理器，记录任务进度
    '''

    def __init__(self):
        self.progress = {}  # {str: Tasker}  task_name: tasker
        self.state_cache = {}  # {str: State}  task_name: status 状态缓存
        self.total = 0  # 总任务数
        self.completed = 0  # 已完成任务数
        self.failed = 0  # 失败任务数
        self.running = 0  # 正在运行任务数
        self.pending = 0  # 待执行任务数
        self.start_time = None  # 开始时间
        self.end_time = None  # 结束时间
    
    def add_tasker(self, task_name: str):
        '''
        添加任务到进度管理器
        '''
        if not task_name:
            return
        
        self.progress[task_name] = None
        self.state_cache[task_name] = None  # 初始化状态缓存
        self.total += 1
        self.pending += 1
        
        if self.start_time:
            return

        # 设置开始时间
        self.start_time = time.time()

    def add_taskers(self, task_names: list):
        '''
        添加多个任务到进度管理器
        '''
        if not task_names:
            return

        for task_name in task_names:
            self.add_tasker(task_name)
    
    def update_progress(self, tasker: Tasker):
        '''
        更新任务进度
        '''
        name = tasker.name
        if name not in self.progress:
            return
        
        # 从状态缓存中获取旧状态
        old_status = self.state_cache.get(name)
        new_status = tasker.status
                
        # 更新任务状态统计
        self._update_status_count(old_status, new_status)
        
        # 更新任务和状态缓存
        self.progress[name] = tasker
        self.state_cache[name] = new_status
        
        # 检查是否所有任务完成
        if not self._is_all_completed():
            return

        self.end_time = time.time()
    
    def _update_status_count(self, old_status, new_status):
        '''更新状态计数'''
        if old_status == new_status:
            return
            
        # 先处理旧状态，减少相应计数
        if old_status:
            if State.is_pending(old_status):
                self.pending -= 1
            elif State.is_running(old_status):
                self.running -= 1
            elif State.is_finished(old_status):
                self.completed -= 1
            elif State.is_failed(old_status):
                self.failed -= 1
        elif new_status and not State.is_pending(new_status):
            self.pending = max(0, self.pending - 1)
        
        # 再处理新状态，增加相应计数
        if State.is_pending(new_status):
            self.pending += 1
        elif State.is_running(new_status):
            self.running += 1
        elif State.is_finished(new_status):
            self.completed += 1
        elif State.is_failed(new_status):
            self.failed += 1
        
        # 最后确保所有计数非负
        self.pending = max(0, self.pending)
        self.running = max(0, self.running)
        self.completed = max(0, self.completed)
        self.failed = max(0, self.failed)

        # print(f"[progress] pending: [{self.pending}] running: [{self.running}] completed: [{self.completed}] failed: [{self.failed}] total: [{self.total}]")
    
    def get_task_progress(self, name: str) -> Tasker:
        '''
        获取指定任务的进度
        '''
        return self.progress.get(name)
    
    def get_all_tasks(self) -> dict:
        '''
        获取所有任务
        '''
        return self.progress.copy()
    
    def get_progress_percentage(self) -> float:
        '''
        获取总体进度百分比
        '''
        if self.total == 0:
            return 0.0
        return (self.completed + self.failed) / self.total * 100
    
    def get_success_rate(self) -> float:
        '''
        获取成功率
        '''
        total_processed = self.completed + self.failed
        if total_processed == 0:
            return 0.0
        return self.completed / total_processed * 100
    
    def get_elapsed_time(self) -> float:
        '''
        获取已用时间（秒）
        '''
        if not self.start_time:
            return 0.0
        end_time = self.end_time if self.end_time else time.time()
        return end_time - self.start_time
    
    def _is_all_completed(self) -> bool:
        '''
        检查是否所有任务都已完成
        '''
        return (self.completed + self.failed) >= self.total
    
    def is_all_finished(self) -> bool:
        '''
        检查是否所有任务都已结束（完成或失败）
        '''
        return self._is_all_completed()
    
    def has_running_tasks(self) -> bool:
        '''
        检查是否有正在运行的任务
        '''
        return self.running > 0
    
    def has_pending_tasks(self) -> bool:
        '''
        检查是否有待执行的任务
        '''
        return self.pending > 0
    
    def get_progress_summary(self) -> dict:
        '''
        获取进度摘要
        '''
        return {
            'total': self.total,
            'completed': self.completed,
            'failed': self.failed,
            'running': self.running,
            'pending': self.pending,
            'progress_percentage': round(self.get_progress_percentage(), 2),
            'success_rate': round(self.get_success_rate(), 2),
            'elapsed_time': round(self.get_elapsed_time(), 2),
            'is_all_finished': self.is_all_finished(),
            'has_running': self.has_running_tasks(),
            'has_pending': self.has_pending_tasks()
        }
    
    def reset(self):
        '''
        重置进度管理器
        '''
        self.progress.clear()
        self.total = 0
        self.completed = 0
        self.failed = 0
        self.running = 0
        self.pending = 0
        self.start_time = None
        self.end_time = None
    
    def __repr__(self) -> str:
        return f"TaskProgress(total={self.total}, completed={self.completed}, failed={self.failed}, running={self.running}, pending={self.pending})"
