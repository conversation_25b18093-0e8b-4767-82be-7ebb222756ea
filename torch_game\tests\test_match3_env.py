"""
测试Match-3环境
"""

import pytest
import numpy as np

from torch_game.env import Match3Env

class TestMatch3Env:
    """测试Match-3环境类"""
    
    def setup_method(self):
        """每个测试方法前的设置"""
        self.env = Match3Env(board_size=6, num_colors=5)
    
    def test_init(self):
        """测试环境初始化"""
        assert self.env.board_size == 6
        assert self.env.num_colors == 5
        assert self.env.board.shape == (6, 6)
        assert np.all(self.env.board >= 0)
        assert np.all(self.env.board < 5)
    
    def test_reset(self):
        """测试环境重置"""
        state = self.env.reset()
        
        # 检查状态是否是一维数组
        assert isinstance(state, np.ndarray)
        assert state.ndim == 1
        
        # 检查状态长度是否正确（棋盘大小的平方）
        assert state.shape[0] == self.env.board_size ** 2
        
        # 检查状态值是否在正确范围内
        assert np.all(state >= 0)
        assert np.all(state < self.env.num_colors)
    
    def test_step_valid_action(self):
        """测试有效动作的步骤"""
        self.env.reset()
        
        # 执行有效动作（假设动作0是有效的）
        action = 0
        next_state, reward, done, info = self.env.step(action)
        
        # 检查返回值类型
        assert isinstance(next_state, np.ndarray)
        assert isinstance(reward, (int, float))
        assert isinstance(done, bool)
        assert isinstance(info, dict)
        
        # 检查状态形状
        assert next_state.shape[0] == self.env.board_size ** 2
    
    def test_step_invalid_action(self):
        """测试无效动作的步骤"""
        self.env.reset()
        
        # 执行无效动作（超出动作空间）
        invalid_action = self.env.action_space.n + 10
        
        # 应该引发异常
        with pytest.raises(Exception):
            self.env.step(invalid_action)
    
    def test_action_space(self):
        """测试动作空间"""
        # 动作空间应该是离散的
        assert self.env.action_space.n == 2 * self.env.board_size * (self.env.board_size - 1)
    
    def test_observation_space(self):
        """测试观察空间"""
        # 观察空间应该是Box
        assert self.env.observation_space.shape[0] == self.env.board_size ** 2
    
    def test_render(self):
        """测试渲染方法"""
        self.env.reset()
        
        # 渲染方法应该不引发异常
        try:
            self.env.render()
        except Exception as e:
            pytest.fail(f"render() raised {e} unexpectedly!")
    
    def test_close(self):
        """测试关闭方法"""
        self.env.reset()
        
        # 关闭方法应该不引发异常
        try:
            self.env.close()
        except Exception as e:
            pytest.fail(f"close() raised {e} unexpectedly!")
    
    def test_multiple_episodes(self):
        """测试多个回合"""
        for _ in range(3):
            state = self.env.reset()
            done = False
            total_reward = 0
            
            while not done:
                action = self.env.action_space.sample()  # 随机动作
                next_state, reward, done, info = self.env.step(action)
                total_reward += reward
                
                # 检查状态转换
                assert next_state.shape == state.shape
                
                state = next_state
            
            # 检查回合是否正常结束
            assert isinstance(total_reward, (int, float))
    
    def test_different_board_sizes(self):
        """测试不同的棋盘大小"""
        for size in [4, 8]:
            env = Match3Env(board_size=size, num_colors=5)
            state = env.reset()
            
            # 检查状态长度是否正确
            assert state.shape[0] == size ** 2
            
            # 检查动作空间
            assert env.action_space.n == 2 * size * (size - 1)
    
    def test_different_num_colors(self):
        """测试不同的颜色数量"""
        for colors in [3, 7]:
            env = Match3Env(board_size=6, num_colors=colors)
            state = env.reset()
            
            # 检查颜色范围
            assert np.all(state >= 0)
            assert np.all(state < colors)