"""
torch_game: 一个基于PyTorch的强化学习游戏框架
"""

# 导入环境模块
from torch_game.core.env.base_env import BaseEnv
from torch_game.core.env.match3_env import Match3Env
from torch_game.core.env.click_match3_env import ClickMatch3Env

# 导入智能体模块
from torch_game.core.agents.base_agent import BaseAgent
from torch_game.core.agents.ppo_agent import PPOAgent

# 导入模型模块
from torch_game.models.policy_network import PolicyNetwork
from torch_game.models.value_network import ValueNetwork

# 导入工具模块
from torch_game.core.utils.buffer import PPOBuffer
from torch_game.core.utils.trainer import Trainer

__all__ = [
    # 环境
    'BaseEnv',
    'Match3Env',
    'ClickMatch3Env',

    # 智能体
    'BaseAgent',
    'PPOAgent',

    # 模型
    'PolicyNetwork',
    'ValueNetwork',

    # 工具
    'PPOBuffer',
    'Trainer'
]

__version__ = '0.1.0'