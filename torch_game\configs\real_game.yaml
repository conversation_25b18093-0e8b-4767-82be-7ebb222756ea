# 真实点击式三消游戏配置文件

# 环境配置
env:
  type: "ClickMatch3Env"
  max_steps: 200        # 每回合最大步数
  n_colors: 6           # 方块颜色数量
  storage_capacity: 7   # 预存区容量
  
  # 游戏连接配置
  game_window: "最强祖师"
  model_path: "models/sanxiao/best.pt"
  simulate_mode: false  # 是否使用模拟模式

# 智能体配置
agent:
  type: "PPOAgent"
  hidden_dims: [512, 256]  # 更大的网络适应复杂状态
  lr: 0.0001              # 较小的学习率，稳定训练
  gamma: 0.995            # 高折扣因子，重视长期奖励
  gae_lambda: 0.97        # GAE lambda参数
  clip_epsilon: 0.15      # PPO裁剪参数
  entropy_coef: 0.01      # 熵系数，鼓励探索
  value_coef: 0.5         # 价值损失系数
  max_grad_norm: 0.5      # 梯度裁剪
  
  # 网络配置
  network:
    activation: "ReLU"
    use_batch_norm: false
    dropout: 0.1
    initialization: "orthogonal"

# 训练配置
training:
  num_episodes: 2000      # 训练回合数
  buffer_capacity: 20000  # 经验缓冲区容量
  batch_size: 128         # 批次大小
  update_frequency: 10    # 更新频率
  num_epochs: 4           # PPO更新轮数
  log_frequency: 10       # 日志频率
  
  # 学习率调度
  lr_schedule:
    type: "linear"
    start_factor: 1.0
    end_factor: 0.1
    total_iters: 2000
  
  # 早停策略
  early_stopping:
    patience: 100
    min_delta: 0.01
    
  # 检查点配置
  checkpointing:
    frequency: 100        # 每100回合保存一次
    keep_best: 5          # 保留最好的5个模型
    save_dir: "checkpoints/real_game"

# 评估配置
evaluation:
  num_episodes: 5         # 评估回合数
  frequency: 50           # 评估频率
  render: true            # 是否显示评估过程
  deterministic: true     # 使用确定性策略

# 奖励配置
reward:
  # 基础奖励
  elimination_reward: 10.0      # 每消除一个方块的奖励
  combo_bonus: 20.0             # 连击额外奖励
  
  # 惩罚
  invalid_action_penalty: -5.0  # 无效动作惩罚
  storage_full_penalty: -10.0   # 预存区满惩罚
  storage_near_full_penalty: -2.0  # 预存区接近满惩罚
  
  # 策略奖励
  strategic_move_bonus: 5.0     # 策略性移动奖励
  efficiency_bonus: 1.0         # 效率奖励

# 日志配置
logging:
  level: "INFO"
  save_to_file: true
  log_dir: "logs/real_game"
  tensorboard: true
  
  # 记录的指标
  metrics:
    - "episode_reward"
    - "episode_length"
    - "action_success_rate"
    - "storage_usage"
    - "elimination_count"
    - "invalid_action_count"

# 可视化配置
visualization:
  plot_training: true
  plot_frequency: 100
  save_plots: true
  plot_dir: "plots/real_game"
  
  # 绘制的图表
  plots:
    - "reward_curve"
    - "length_curve"
    - "success_rate"
    - "loss_curves"

# 实验配置
experiment:
  name: "real_game_ppo"
  description: "PPO agent playing real click-style match-3 game"
  tags: ["ppo", "real_game", "match3", "click_style"]
  
  # 随机种子
  seed: 42
  
  # 设备配置
  device: "auto"  # auto, cpu, cuda
  
  # 并行配置
  num_workers: 1  # 真实游戏通常只能单线程

# 调试配置
debug:
  verbose: true
  save_states: false
  save_actions: false
  profile_performance: false
  
  # 调试模式下的特殊设置
  debug_mode: false
  max_debug_episodes: 10
