import argparse
import os
import cv2
from PIL import Image, ImageDraw
from ultralytics import YOL<PERSON>

def get_latest_model_path(base_dir, is_train):
    """
    获取指定目录下最新的模型路径。
    :param base_dir: 模型保存的基础目录
    :param is_train: 是否为训练模式
    :return: 最新模型的路径，如果没有找到则返回None
    """
    if not os.path.isdir(base_dir):
        return

    model_name = 'last.pt' if is_train else 'best.pt'

    # 查找所有以 'train'开头的目录
    target_dirs = [d for d in os.listdir(base_dir) if d.startswith('train')]
    if not target_dirs:
        return

    # 定义一个函数，用于提取目录名中的数字，如果没有数字则返回0
    def get_dir_num(d):
        num_str = d.replace('train', '')
        if num_str.isdigit():
            return int(num_str)
        else:
            return 0

    # 找到最新的目录编号
    latest_dir_num = max([get_dir_num(d) for d in target_dirs])
    latest_dir = f'train{latest_dir_num}' if latest_dir_num > 0 else 'train'

    # 构建最新模型的路径
    model_path = os.path.join(base_dir, latest_dir, 'weights', model_name)
    if not os.path.exists(model_path):
        return
    return model_path

def process_results(results, conf_threshold=0.25):
    """处理YOLOv8结果并返回原始标注格式"""
    output = []

    for box in results.boxes:
        conf = box.conf.item()
        if conf < conf_threshold:
            continue
            
        cls_id = int(box.cls.item())
        xywhn = box.xywhn[0].tolist()

        data_line = f"{cls_id} {xywhn[0]:.6f} {xywhn[1]:.6f} {xywhn[2]:.6f} {xywhn[3]:.6f}"
        output.append(data_line)

    return output

def write_predict_labels(annotations, image_path):
    '''
    r"trains\data\sanxiao\tests\images\20250623130958.jpg"
    '''
    base_dir = os.path.dirname(os.path.dirname(image_path))
    labels_path = os.path.join(base_dir, 'labels')
    if not os.path.exists(labels_path):
        os.makedirs(labels_path)
    
    image_name = os.path.basename(image_path)
    label_path = os.path.join(labels_path, image_name.replace('.jpg', '.txt'))

    with open(label_path, 'a') as f:
        for line in annotations:
            f.write(line + '\n')

def model_predict_tests(model, tests_dir):
    """
    对指定目录下的所有图片进行推理，并将结果保存到labels目录下。
    :param model: YOLO模型
    :param tests_dir: 测试图片目录
    """
    image_dir = os.path.join(tests_dir, 'images')
    label_dir = os.path.join(tests_dir, 'labels')
    if not os.path.exists(label_dir):
        os.makedirs(label_dir)

    for image_name in os.listdir(image_dir):
        image_path = os.path.join(image_dir, image_name)

        image = cv2.imread(image_path)
        image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))

        results = model.predict(
            image,
            imgsz=640,
            conf=0.5,
            # max_det=10,
        )
        annotations = process_results(results[0])
        write_predict_labels(annotations, image_path)
    

def main(args, project):
    if args.mode == 'train':
        # 加载最新的模型
        model_path = get_latest_model_path(project, is_train=True)
        print("model_path:", model_path)
        if model_path:
            model = YOLO(model_path)
        else:
            print("没有找到训练好的模型，将使用预训练的yolov8n模型。")
            model = YOLO(r'trains\v8_models\yolov8n.pt')

        # 训练模型    
        model.train(
            data=args.data, 
            epochs=args.epochs, 
            imgsz=args.imgsz, 
            batch=args.batch, 
            workers=args.workers, 
            device=args.device,
            patience=10,
            project=project,
            name="train",
        )
    elif args.mode == 'predict':
        model_path = get_latest_model_path(project, is_train=False)
        print("model_path:", model_path)
        if model_path:
            model = YOLO(model_path)
        else:
            print("没有找到训练好的模型，将使用预训练的yolov8s模型。")
            model = YOLO(args.model)
        
        # 推理模型
        results = model(args.img, save=True, show=True, project=project, name="predict")
        
        annotations = process_results(results[0])
        for line in annotations:
            print(line)

        write_predict_labels(annotations, args.img)

    elif args.mode == 'batch':
        model_path = get_latest_model_path(project, is_train=False)
        print("model_path:", model_path)
        if model_path:
            model = YOLO(model_path)
        else:
            print("没有找到训练好的模型，将使用预训练的yolov8s模型。")
            model = YOLO(args.model)

        model_predict_tests(model, args.tests_dir)


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='控制YOLOv8的训练和推理')
    parser.add_argument('--mode', choices=['train', 'predict', 'batch'], required=True, help='选择训练或推理模式')
    parser.add_argument('--model', type=str, default=r'trains\v8_models\yolov8n.pt', help='指定要使用的模型路径')
    parser.add_argument('--data', type=str, default=r"trains\data\sanxiao\sanxiao.yaml", help='指定训练数据的路径')
    parser.add_argument('--epochs', type=int, default=1500, help='训练的轮数')
    parser.add_argument('--imgsz', type=int, default=640, help='输入图像的大小')
    parser.add_argument('--batch', type=int, default=16, help='批次大小')
    parser.add_argument('--workers', type=int, default=2, help='工作线程数')
    parser.add_argument('--device', type=int, default=0, help='使用的GPU设备编号')
    parser.add_argument('--img', type=str, default=r"trains\data\sanxiao\tests\images\20250623223920.jpg", help='指定要推理的图像路径')
    parser.add_argument('--tests_dir', type=str, default=r"trains\data\sanxiao\tests", help='指定要推理的图像所在的目录')
    args = parser.parse_args()

    project = r'trains\runs\detect\sanxiao'

    main(args, project)
