'''
图色控制器

工作流程：
1. 初始化
2. 客户区截图
3. 处理，比如切小图片、区域找图、区域找色等
'''

import cv2  # OpenCV图像处理库
import dxcam  # 高性能截图库
import numpy as np  # 数组处理库
from PIL import Image, ImageChops  # PIL图像处理库

from typing import Tuple, Union, List, Any

from utils.window import Window

class ImageController:
    
    def __init__(self, hwnd: int):
        self.hwnd = hwnd

        self.window = Window()
        
        # 获取屏幕分辨率
        self.screen_w, self.screen_h = self.window.get_screen_size()
        print(f"屏幕分辨率: {self.screen_w}x{self.screen_h}")
        
        # 初始化视频录制区域
        self.camera_rect = self.get_window_rect()

        try:
            self.camera = dxcam.create(
                output_color="BGR",
                output_idx=0,  # 默认显示器
                max_buffer_len=64  # 帧缓冲区大小
            )
            # 初始化视频录制状态
            self.is_recording = False
            self.target_fps = 30  # 默认30fps
            self.video_writer = None  # 视频写入器
        except Exception as e:
            raise RuntimeError(f"截图初始化失败: {str(e)}")

    def get_window_rect(self):
        '''获取窗口的矩形坐标'''
        left, top, right, bottom = self.window.get_window_rect(self.hwnd)

        left = max(0, left)
        top = max(0, top)
        right = min(self.screen_w, right)
        bottom = min(self.screen_h, bottom)

        return (left, top, right, bottom)
    
    def _create_video_writer(self, output_path, rect, fps):
        '''创建视频写入器'''

        # 如果指定了输出路径，创建视频写入器
        if not output_path:
            return

        width = rect[2] - rect[0]
        height = rect[3] - rect[1]
        # 使用 XVID 编码器，这是一个更通用的编码器
        fourcc = cv2.VideoWriter_fourcc(*'XVID')
        # 设置视频文件扩展名为.avi
        if not output_path.lower().endswith('.avi'):
            output_path = output_path.rsplit('.', 1)[0] + '.avi'
        self.video_writer = cv2.VideoWriter(
            output_path,
            fourcc,
            fps,
            (width, height)
        )

    def _release_video_writer(self):
        '''释放视频写入器'''
        if self.video_writer:
            self.video_writer.release()
            self.video_writer = None

    def start_recording(self, target_fps: int = 30, output_path: str = ""):
        '''开始视频录制
        Args:
            target_fps: 目标帧率
            output_path: 视频保存路径，为空则不保存
        '''
        if self.is_recording:
            return
            
        try:
            # 使用当前窗口位置和大小启动视频录制
            rect = self.get_window_rect()
            self.camera.start(
                target_fps=target_fps,
                video_mode=True,
                region=rect
            )
            
            # 如果指定了输出路径，创建视频写入器
            self._create_video_writer(output_path, rect, target_fps)
            
            # 只有在成功启动后才设置状态
            self.is_recording = True
            self.target_fps = target_fps
        except Exception as e:
            self._release_video_writer()
            print(f"启动录制失败: {str(e)}")
            
    def stop_recording(self):
        '''停止视频录制'''
        if self.is_recording:
            self.camera.stop()
            self._release_video_writer()
            self.is_recording = False

    def get_frame(self) -> Union[np.ndarray, None]:
        '''获取最新帧'''
        try:
            # 检查并更新视频录制区域
            self.update_recording_region()

            # 从视频流中获取最新帧
            frame = self.camera.get_latest_frame()
            if frame is None or frame.size == 0:
                return

            return frame
        except Exception as e:
            print(f"获取帧失败: {str(e)}")

    def write_frame(self, frame: np.ndarray) -> None:
        '''将帧写入视频
        Args:
            frame: 要写入的帧，必须是BGR格式的numpy数组
        '''
        if self.is_recording and self.video_writer:
            self.video_writer.write(frame)

    def update_recording_region(self) -> bool:
        '''检测窗口位置是否发生变化，并更新视频录制区域'''
        if not self.is_recording:
            return False

        # 获取当前窗口位置和大小
        current_rect = self.get_window_rect()

        # 检查窗口是否移动或改变大小
        if current_rect != self.camera_rect:
            self.camera_rect = current_rect

            # 重新启动录制以更新区域
            self.camera.stop()
            self.camera.start(
                target_fps=self.target_fps,
                video_mode=True,
                region=current_rect
            )
            return True
        return False
        
    def calculate_region(self, region: Tuple[int, int, int, int]) -> Tuple[int, int, int, int]:
        '''计算区域的绝对坐标
        Args:
            region: 相对于客户区的区域坐标 (left, top, right, bottom)
        Returns:
            Tuple[int, int, int, int]: 绝对坐标 (left, top, right, bottom)
        '''
        try:
            # 获取客户区域坐标
            client_left, client_top = self.window.client_to_screen(self.hwnd, (0, 0))
            
            # 计算绝对坐标
            abs_left = max(0, client_left + region[0])
            abs_top = max(0, client_top + region[1])
            abs_right = min(self.screen_w, client_left + region[2])
            abs_bottom = min(self.screen_h, client_top + region[3])

            # 确保区域有效
            if abs_right <= abs_left or abs_bottom <= abs_top:
                print("无效的区域范围")
                return (0, 0, 0, 0)
            
            return (abs_left, abs_top, abs_right, abs_bottom)
        except Exception as e:
            print(f"计算区域坐标失败: {str(e)}")
            return (0, 0, 0, 0)

    def capture_client_area(self) -> Image.Image:
        '''截取窗口客户区'''
        try:
            # 如果正在录制视频，使用get_latest_frame方法获取最新帧
            if self.is_recording:
                frame = self.camera.get_latest_frame()
                if frame is None or frame.size == 0:
                    return
                # 转换为PIL格式
                return Image.fromarray(cv2.cvtColor(frame, cv2.COLOR_BGR2RGB))

            # 使用dxcam高性能截图，指定区域
            frame = self.camera.grab(region=self.get_window_rect())
            if frame is None or frame.size == 0:
                return 
            # 转换为PIL格式
            return Image.fromarray(cv2.cvtColor(frame, cv2.COLOR_BGR2RGB))
        except Exception as e:
            raise RuntimeError(f"客户区截图失败: {str(e)}")

    def capture_region(self, region: Tuple[int, int, int, int]) -> Image.Image:
        '''截取指定区域的图片
        Args:
            region: 相对于客户区的区域坐标 (left, top, right, bottom)
        Returns:
            PIL.Image: 截取的图片
        '''
        try: 
            # 如果正在录制视频，使用get_latest_frame方法获取最新帧
            if self.is_recording:
                frame = self.camera.get_latest_frame()
                if frame is None or frame.size == 0:
                    return

                # 获取窗口位置和大小
                window_rect = self.get_window_rect()
                # 获取窗口客户区的位置
                client_left, client_top = self.window.client_to_screen(self.hwnd, (0, 0))
                
                # 计算区域在录制帧中的相对位置
                rel_left = client_left + region[0] - window_rect[0]
                rel_top = client_top + region[1] - window_rect[1]
                rel_right = client_left + region[2] - window_rect[0]
                rel_bottom = client_top + region[3] - window_rect[1]
                
                # 确保坐标在有效范围内
                rel_left = max(0, rel_left)
                rel_top = max(0, rel_top)
                rel_right = min(frame.shape[1], rel_right)
                rel_bottom = min(frame.shape[0], rel_bottom)
                
                # 从帧中截取指定区域
                region_frame = frame[rel_top:rel_bottom, rel_left:rel_right]
                
                # 转换为PIL格式
                return Image.fromarray(cv2.cvtColor(region_frame, cv2.COLOR_BGR2RGB))

            # 使用dxcam高性能截图，指定区域
            frame = self.camera.grab(region=self.calculate_region(region))
            if frame is None or frame.size == 0:
                return
            
            # 转换为PIL格式
            return Image.fromarray(cv2.cvtColor(frame, cv2.COLOR_BGR2RGB))
        except Exception as e:
            raise RuntimeError(f"区域截图失败: {str(e)}")

    def find_color(self, img: Image.Image, target_color: tuple, tolerance=10) -> Union[None, Tuple[int, int]]:
        '''在图片中寻找指定颜色的位置'''
        try:
            # 转换为NumPy数组并确保BGR格式
            img_array = cv2.cvtColor(np.array(img), cv2.COLOR_RGB2BGR)

            # 将RGB目标颜色转换为BGR格式
            target_color_bgr = (target_color[2], target_color[1], target_color[0])
            
            # 创建颜色范围
            lower = np.array([max(0, c - tolerance) for c in target_color_bgr])
            upper = np.array([min(255, c + tolerance) for c in target_color_bgr])
            
            # 创建颜色掩码
            mask = cv2.inRange(img_array, lower, upper)
            
            # 找到匹配的像素位置
            matches = np.where(mask > 0)
            if len(matches[0]) == 0:
                return
            
            # 返回第一个匹配位置
            return (matches[1][0], matches[0][0])  # (x, y)
        except Exception as e:
            print(f"颜色匹配失败: {str(e)}")

    def find_color_in_region(self, target_color: tuple, region: tuple, tolerance=10) -> Union[None, Tuple[int, int]]:
        '''在指定区域中寻找指定颜色的位置
        Args:
            target_color: 目标颜色RGB值 (r, g, b)
            region: 相对于客户区的区域坐标 (left, top, right, bottom)
            tolerance: 颜色匹配容差值，默认10
        Returns:
            None 或 (x, y)，返回的坐标是相对于客户区的坐标
        '''
        # 截取指定区域
        region_img = self.capture_region(region)
        if not region_img:
            return
            
        # 在区域中寻找颜色
        result = self.find_color(region_img, target_color, tolerance)
        if not result:
            return
            
        # 转换为相对于客户区的坐标
        x, y = result
        return (region[0] + x, region[1] + y)

    def find_image(self, main_img: Image.Image, sub_img: Image.Image, threshold=0.8) -> Union[None, Tuple[float, Tuple[int, int], Tuple[int, int]]]:
        '''在大图中寻找小图位置'''
        try:
            # 检查图像尺寸
            main_size = main_img.size
            sub_size = sub_img.size
            if sub_size[0] > main_size[0] or sub_size[1] > main_size[1]:
                print("小图尺寸大于大图，无法进行匹配")
                return 

            # 转换为OpenCV格式
            main = cv2.cvtColor(np.array(main_img), cv2.COLOR_RGB2BGR)
            sub = cv2.cvtColor(np.array(sub_img), cv2.COLOR_RGB2BGR)
            
            # 使用模板匹配
            res = cv2.matchTemplate(main, sub, cv2.TM_CCOEFF_NORMED)
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(res)
            
            if max_val <= threshold:
                return 

            h, w = sub.shape[:2]
            top_left = max_loc
            bottom_right = (top_left[0] + w, top_left[1] + h)
            return (max_val, top_left, bottom_right)
        except Exception as e:
            print(f"图片匹配失败: {str(e)}")

    def find_image_in_region(self, sub_img: Image.Image, region: tuple, threshold=0.8) -> Union[None, Tuple[float, Tuple[int, int], Tuple[int, int]]]:
        '''在指定区域中寻找小图位置
        Args:
            sub_img: 要查找的小图
            region: 相对于客户区的区域坐标 (left, top, right, bottom)
            threshold: 匹配阈值，越大越精确
        Returns:
            None 或 (相似度, (左上角x, 左上角y), (右下角x, 右下角y))
            注意：返回的坐标是相对于客户区的坐标
        '''
        # 截取指定区域
        main_img = self.capture_region(region)
        if not main_img:
            return 
            
        # 在区域中寻找小图
        result = self.find_image(main_img, sub_img, threshold)
        if not result:
            return 
            
        # 转换为相对于客户区的坐标
        max_val, top_left, bottom_right = result
        rel_top_left = (region[0] + top_left[0], region[1] + top_left[1])
        rel_bottom_right = (region[0] + bottom_right[0], region[1] + bottom_right[1])
         
        return (max_val, rel_top_left, rel_bottom_right)

    def image_similarity(self, img1, img2):
        """ 计算两图结构相似度 [0-1] """
        # 确保图像尺寸一致
        if img1.size != img2.size:
            raise ValueError("Images must have same size")

        # 计算差异图
        diff = ImageChops.difference(img1.convert('RGB'), img2.convert('RGB'))
        
        # 计算总差异值（每个像素的RGB三通道差异之和）
        sum_diff = sum(r + g + b for (r, g, b) in diff.getdata())
        
        # 计算最大可能差异值（255 * 3 channels）
        max_diff = 255 * 3 * diff.size[0] * diff.size[1]
        
        return 1 - (sum_diff / max_diff)
        
    def close(self):
        '''释放资源'''
        try:
            if self.is_recording:
                self.stop_recording()
            if hasattr(self, 'camera') and self.camera is not None:
                self.camera.stop()
                self.camera = None
        except Exception as e:
            print(f"释放资源失败: {str(e)}")