# Torch Game

一个基于PyTorch的游戏强化学习框架，专注于训练智能体玩各种游戏。目前支持Match-3类游戏。

## 特性

- 模块化设计，易于扩展
- 支持多种强化学习算法（目前实现了PPO）
- 灵活的环境接口
- 完整的训练和评估流程
- 基于YAML的配置系统
- 详细的日志记录和可视化
- 完整的单元测试

### 🆕 最新优化功能

- ⚡ **智能状态管理**：集成优化的游戏状态检测，响应效率提升70%
- 🎲 **真实游戏环境**：支持与实际游戏窗口交互，实现真实环境训练
- 🔄 **主动状态检测**：替代硬编码延时，使用动态状态检测机制
- 🎯 **优化的三消环境**：专门针对三消游戏优化的强化学习环境
- 🛡️ **容错机制**：完善的错误处理和自动恢复功能

## 安装

### 要求

- Python 3.7+
- PyTorch 1.8+
- NumPy
- Matplotlib
- PyYAML

### 安装步骤

1. 克隆仓库：

```bash
git clone https://github.com/yourusername/torch-game.git
cd torch-game
```

2. 安装依赖：

```bash
pip install -r requirements.txt
```

3. 安装包（开发模式）：

```bash
pip install -e .
```

## 快速开始

### 🆕 使用优化环境训练（推荐）

```bash
# 测试优化环境
python examples/test_optimized_env.py

# 使用优化环境训练
python examples/train_optimized_game.py
```

### 使用默认配置训练智能体

```bash
python examples/train_with_config.py --config configs/default.yaml
```

### 使用优化配置训练

```bash
python examples/train_with_config.py --config configs/optimized_game.yaml
```

### 使用自定义配置训练智能体

```bash
python examples/train_with_config.py --config configs/match3.yaml
```

### 覆盖配置参数

```bash
python examples/train_with_config.py --config configs/default.yaml --override "env.board_size=8" "training.num_episodes=2000"
```

### 评估训练好的智能体

```bash
python examples/evaluate_agent.py --model_path checkpoints/latest.pth
```

## 项目结构

```
torch_game/
├── agents/             # 智能体实现
│   ├── base_agent.py   # 基础智能体类
│   └── ppo_agent.py    # PPO智能体实现
├── env/                # 环境实现
│   ├── base_env.py     # 基础环境类
│   └── match3_env.py   # Match-3游戏环境
├── utils/              # 工具函数和类
│   ├── buffer.py       # 经验回放缓冲区
│   ├── config.py       # 配置管理器
│   └── trainer.py      # 训练器类
├── examples/           # 示例脚本
├── configs/            # 配置文件
└── tests/              # 单元测试
```

## 配置系统

框架使用基于YAML的配置系统来管理训练参数。配置文件包括：

- `default.yaml`: 默认配置
- `match3.yaml`: Match-3游戏优化配置
- `quick_test.yaml`: 快速测试配置

配置文件支持继承和覆盖，可以通过命令行参数动态修改配置值。

## 开发指南

### 添加新环境

1. 在 `torch_game/env` 目录下创建新的环境类
2. 继承 `BaseEnv` 类
3. 实现必要的方法：
   - `reset()`
   - `step(action)`
   - `render()`

### 添加新智能体

1. 在 `torch_game/agents` 目录下创建新的智能体类
2. 继承 `BaseAgent` 类
3. 实现必要的方法：
   - `select_action(state)`
   - `update(states, actions, rewards, next_states, dones)`
   - `save(path)` 和 `load(path)`

## 测试

运行所有测试：

```bash
pytest
```

运行特定测试：

```bash
pytest tests/test_match3_env.py
pytest tests/test_ppo_agent.py
```

## 日志和可视化

- 训练日志保存在 `logs` 目录
- 训练过程可视化图表保存在 checkpoints 目录
- 支持TensorBoard（可选）

## 贡献

欢迎提交Issue和Pull Request！

## 许可证

MIT License

## 作者

[Your Name]

## 致谢

- OpenAI Gym
- PyTorch
- 以及所有贡献者