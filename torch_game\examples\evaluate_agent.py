"""
评估已训练智能体的示例脚本
"""

import os
import argparse
import numpy as np
import time

from torch_game.env import Match3Env
from torch_game.agents import PPOAgent
from torch_game.utils import Trainer

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='Evaluate a trained agent')
    
    # 环境参数
    parser.add_argument('--board_size', type=int, default=6, help='Size of the game board')
    parser.add_argument('--num_colors', type=int, default=5, help='Number of colors in the game')
    
    # 智能体参数
    parser.add_argument('--hidden_dims', type=int, nargs='+', default=[256, 128], help='Hidden dimensions of the networks')
    
    # 评估参数
    parser.add_argument('--num_episodes', type=int, default=10, help='Number of evaluation episodes')
    parser.add_argument('--max_steps', type=int, default=100, help='Maximum steps per episode')
    parser.add_argument('--render', action='store_true', help='Render the environment during evaluation')
    parser.add_argument('--delay', type=float, default=0.5, help='Delay between steps when rendering (seconds)')
    
    # 模型参数
    parser.add_argument('--model_path', type=str, required=True, help='Path to the trained model')
    
    return parser.parse_args()

def main():
    """主函数"""
    # 解析参数
    args = parse_args()
    
    # 检查模型文件是否存在
    if not os.path.exists(args.model_path):
        raise FileNotFoundError(f"Model file not found: {args.model_path}")
    
    # 创建环境
    env = Match3Env(
        board_size=args.board_size,
        num_colors=args.num_colors
    )
    
    # 计算状态和动作维度
    state_dim = env.observation_space.shape[0]
    action_dim = env.action_space.n
    
    # 创建智能体
    agent = PPOAgent(
        state_dim=state_dim,
        action_dim=action_dim,
        hidden_dims=args.hidden_dims
    )
    
    # 加载训练好的模型
    print(f"Loading model from {args.model_path}")
    agent.load(args.model_path)
    
    # 创建训练器
    trainer = Trainer(
        env=env,
        agent=agent,
        buffer_capacity=1000,  # 不重要，因为我们只是评估
        batch_size=64,         # 不重要，因为我们只是评估
        update_frequency=4,    # 不重要，因为我们只是评估
        log_frequency=1,       # 不重要，因为我们只是评估
        save_dir=".",          # 不重要，因为我们只是评估
        render=False           # 我们会在下面手动控制渲染
    )
    
    # 手动评估（带有延迟渲染）
    total_reward = 0
    total_length = 0
    
    for episode in range(args.num_episodes):
        print(f"\nEpisode {episode+1}/{args.num_episodes}")
        
        state = env.reset()
        episode_reward = 0
        episode_length = 0
        done = False
        
        if args.render:
            env.render()
            time.sleep(args.delay)
        
        while not done and episode_length < args.max_steps:
            # 选择动作
            action, _ = agent.select_action(state)
            
            # 执行动作
            next_state, reward, done, info = env.step(action)
            
            # 更新状态和统计信息
            state = next_state
            episode_reward += reward
            episode_length += 1
            
            # 打印步骤信息
            print(f"  Step {episode_length}: Action={action}, Reward={reward:.2f}, Done={done}")
            
            # 渲染环境
            if args.render:
                env.render()
                time.sleep(args.delay)
        
        # 更新总统计信息
        total_reward += episode_reward
        total_length += episode_length
        
        # 打印回合信息
        print(f"  Episode reward: {episode_reward:.2f}")
        print(f"  Episode length: {episode_length}")
    
    # 打印总体评估结果
    avg_reward = total_reward / args.num_episodes
    avg_length = total_length / args.num_episodes
    
    print("\nEvaluation results:")
    print(f"  Average reward: {avg_reward:.2f}")
    print(f"  Average episode length: {avg_length:.2f}")
    print(f"  Total reward: {total_reward:.2f}")
    print(f"  Total episodes: {args.num_episodes}")

if __name__ == "__main__":
    main()