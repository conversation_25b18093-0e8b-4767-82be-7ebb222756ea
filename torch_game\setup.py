from setuptools import setup, find_packages

with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

with open("requirements.txt", "r", encoding="utf-8") as f:
    requirements = f.read().splitlines()
    # 过滤掉注释行和空行
    requirements = [r for r in requirements if not r.startswith('#') and r.strip()]

setup(
    name="torch_game",
    version="0.1.0",
    author="Your Name",
    author_email="<EMAIL>",
    description="A PyTorch-based reinforcement learning framework for games",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/yourusername/torch-game",
    packages=find_packages(),
    classifiers=[
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.7",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Intended Audience :: Science/Research",
        "Development Status :: 3 - Alpha",
    ],
    python_requires=">=3.7",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=6.0.0",
            "pytest-cov>=2.10.0",
            "black>=21.5b2",
            "isort>=5.9.1",
            "flake8>=3.9.2",
        ],
        "viz": [
            "tensorboard>=2.4.0",
            "wandb>=0.12.0",
        ],
    },
)