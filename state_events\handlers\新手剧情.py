import pyautogui
import random
import time
from collections import namedtuple


from ..state import State
from assist import AssistBasicToolkit

import logging
logger = logging.getLogger(__name__)

class 新手剧情:
    combat_params = namedtuple('combat_params', ['option', 'bool', 'description'])
    combat_selector = {
        1: combat_params('1', True, "仅战斗"),
        2: combat_params('2', False, "无战斗"),
    }
    operate_params = namedtuple('operate_params', ['option', 'bool', 'description'])
    operate_selector = {
        1: operate_params('1', True, "操作1次"),
        2: operate_params('2', False, "无操作"),
    }
    plot_params = namedtuple('plot_params', ['option', 'bool', 'description'])
    plot_selector = {
        1: plot_params('1', True, "按esc退出"),
        2: plot_params('2', True, "需要等待"),
        3: plot_params('3', False, "无剧情"),
    }

    task_params = namedtuple('task_params', ['name', 'click', 'dialogue', 'exit', 'combat', 'operate', 'plot'])
    task_selector = {
        "大梦初醒": task_params("大梦初醒", "蓝衣", "请问你在干什么", "取消", 2, 2, 3),
        "解救同伴": task_params("解救同伴", "土堆", "我把你弄出来", "取消", 2, 2, 3),
        "误会再生": task_params("误会再生", "紫怨", "需要你提点", "取消", 2, 2, 3),
        "小试身手": task_params("小试身手", "猎户", "职责所在", "取消", 1, 2, 3),
        "南瓜赠礼": task_params("南瓜赠礼", "南瓜", "也不知会孵化出什么", "取消", 2, 1, 3),
        "礼轻义重": task_params("礼轻义重", "南瓜", "有了这时间宠", "取消", 2, 2, 3),
        "觅得帮手": task_params("觅得帮手", "南瓜", "这就捉了它", "取消", 1, 2, 3),
        "海神集会": task_params("海神集会", "南瓜", "这就是海神的集会", "", 2, 2, 1),
        "加入混战": task_params("加入混战", "紫怨", "帮猎户那边", "帮南瓜那边", 2, 2, 3),
        "打败南瓜": task_params("打败南瓜", "南瓜", "你太冲动了", "取消", 1, 2, 3),
        "劝说南瓜": task_params("劝说南瓜", "南瓜", "别打了", "", 2, 2, 1),
        "海神出现": task_params("海神出现", "海神", "你怎么帮人实现愿望", "取消", 2, 2, 3),
        "祸事之因": task_params("祸事之因", "海神", "你且说说渔村海啸的阴谋是什么", "取消", 2, 2, 2),
        "解决之道": task_params("解决之道", "海神", "", "取消", 2, 2, 3),
        "怒从心起": task_params("怒从心起", "海神", "我们不会让你再鬼话连篇", "取消", 1, 2, 3),
        "将军为何": task_params("将军为何", "鬼将军", "你这家伙到底是什么身份", "取消", 2, 2, 1),
        "惊变突发": task_params("惊变突发", "村长夫人", "我会去把村长他们救回来的", "取消", 2, 2, 1),
        "珊瑚海岛": task_params("珊瑚海岛", "珊瑚海岛", "", "", 2, 2, 1),
        "神秘海螺": task_params("神秘海螺", "熟悉的声音", "仔细观察", "", 2, 2, 1),
        "三族少年": task_params("三族少年", "仙族少年", "很抱歉", "取消", 2, 2, 3),
        "海面异动": task_params("海面异动", "龙族少年", "什么动静", "", 2, 2, 1),
        "仙族质疑": task_params("仙族质疑", "仙族少年", "其实我和紫怨", "取消", 2, 2, 3),
        "驱散鬼气": task_params("驱散鬼气", "传音海螺", "", "", 2, 1, 3),
        "调皮鬼话": task_params("调皮鬼话", "调皮鬼", "回答他的问题", "追问鬼将军的线索", 2, 2, 1),
        "鬼魂往事": task_params("鬼魂往事", "美女鬼", "夸她貌美", "打听消息", 2, 2, 1),
        "海啸谜团": task_params("海啸谜团", "才子鬼", "为何而来", "取消", 2, 2, 1),
        "逃亡少女": task_params("逃亡少女", "少女", "你是渔村的人", "取消", 2, 2, 3),
        "紫怨提议": task_params("紫怨提议", "紫怨", "我准备好了", "取消", 2, 2, 3),
        "彼岸寻人": task_params("彼岸寻人", "彼岸花", "", "", 2, 1, 3),
        "竟是中计": task_params("竟是中计", "鬼将军", "这一切都是你", "取消", 2, 2, 3),
        "五族齐心": task_params("五族齐心", "鬼将军", "不试试怎么知道", "取消", 1, 2, 3),
        "其情可怜": task_params("其情可怜", "鬼新娘", "未过门的妻子", "取消", 2, 2, 3),
        "永生真相": task_params("永生真相", "紫怨", "", "", 2, 2, 2),
        "先民往事": task_params("先民往事", "紫怨", "你知道了什么", "取消", 2, 2, 1),
        "此恨绵绵": task_params("此恨绵绵", "鬼将军", "世事无常", "取消", 2, 2, 3),
        "再入轮回": task_params("再入轮回", "生死簿", "", "", 2, 1, 3),
        "另寻他法": task_params("另寻他法", "紫怨", "为何消失了", "取消", 2, 2, 3),
        "神秘封印": task_params("神秘封印", "神秘封印", "无声无息", "取消", 1, 2, 1),
        "当众忏悔": task_params("当众忏悔", "村长", "那我试试", "", 2, 2, 1),
        "成全遗憾": task_params("成全遗憾", "村长", "没问题", "取消", 2, 2, 3),
        "采购物资": task_params("采购物资", "杂货店", "村长让我来", "取消", 2, 2, 3),
        "交付物资": task_params("交付物资", "村长", "来了", "就来看看你", 2, 1, 3),
        "项链系情": task_params("项链系情", "鬼将军", "成亲之事", "取消", 2, 2, 3),
        "修复项链": task_params("修复项链", "项链", "", "", 2, 1, 1),
        "终成眷属": task_params("终成眷属", "鬼将军", "项链修复完成了", "", 2, 2, 1),
        "力挽狂澜": task_params("力挽狂澜", "鬼将军", "我不会让你再一次伤害村民", "取消", 1, 2, 1),
    }

    def __init__(self):
        self.hwnd = 0

        self.parent = 0
        self.assist: AssistBasicToolkit = None

        self.task_region = (14, 308, 295, 528)

    @classmethod
    def created(cls, hwnd):
        self = cls()
        self.hwnd = hwnd
        return self

    def register_assist_objects(self, parent, assist: AssistBasicToolkit):
        self.parent = parent
        self.assist = assist
        self.window_ops = self.assist.window_ops
        self.screenshot = self.assist.screenshot
        self.detection = self.assist.detection
        self.image_search = self.assist.image_search
        self.ocr = self.assist.ocr

    def output_formater(self, state, reason='', callback=None, *args, **kwargs):
        return {
            'state': state,
            'reason': reason,
            'callback': callback,
            'args': args,
            'kwargs': kwargs
        }

    def start(self):
        if not self.assist:
            logger.error("AssistBasicToolkit 未注册")
            return self.output_formater(state=State.FAILED, reason='AssistBasicToolkit 未注册', callback=None)
        return self.output_formater(state=State.RUNNING, reason='开始任务检测', callback=self.任务检测)


    def stop(self):
        return self.output_formater(state=State.FINISHED, reason='', callback=None)

    def human_click(self, x, y):
            """模拟人类点击（带随机轨迹）"""
            # 保存初始位置
            start_x, start_y = pyautogui.position()
            
            # 生成移动路径
            steps = random.randint(10, 20)
            for i in range(steps):
                t = i / steps
                # 使用贝塞尔曲线添加随机性
                current_x = start_x + (x - start_x) * t + random.uniform(-3, 3)
                current_y = start_y + (y - start_y) * t + random.uniform(-2, 2)
                pyautogui.moveTo(current_x, current_y, duration=0.001)
            
            # 最终点击
            pyautogui.click(x, y, duration=random.uniform(0.05, 0.15))
        
    def 战斗检测(self):
        rect_pos = (1450, 940, 1550, 1000)
        target = '自动'
        results = self.assist.find_ocr_position(
            target,
            rect_pos,
        )
        if not results:
            print(f"未识别到[{target}]")
            return self.output_formater(state=State.FAILED, reason=f'未识别到[{target}]', callback=None)
        print(f'识别到[{target}]，results:', results)
        text, ltps, rbps = results[0]
        print(f"[{target}]数据位置: {ltps}, {rbps}, text: {text}")
        left, top = ltps[0] + (rbps[0] - ltps[0]) / 2, ltps[1] + (rbps[1] - ltps[1]) / 2
        print(f"识别到[{target}]位置: {left}, {top}")
        left, top = self.assist.window_ops.client_to_screen(self.parent, (left, top))
        print(f"转换为屏幕坐标: {left}, {top}")
        pyautogui.moveTo(left, top)
        return self.output_formater(state=State.FINISHED, reason='数据识别正常', callback=None)
    
    def 战斗时间检测(self):
        rect_pos = (715, 175, 865, 265)
        target = '战斗时间'
        results = self.assist.find_color_text_regions(
            rect_pos, 
            color_rgb='#ff0000', 
            similarity=0.8
        )
        if not results:
            print(f"未识别到[{target}]")
            return self.output_formater(state=State.FAILED, reason=f'未识别到[{target}]', callback=None)
        print(f'识别到[{target}]，results:', results)
        sim, ltps, rbps, text = results[0]
        text = text.rstrip('[')  # 去除末尾的左中括号
        print(f"[{target}]数据位置: {ltps}, {rbps}, text: {text}")
        left, top = ltps[0] + (rbps[0] - ltps[0]) / 2, ltps[1] + (rbps[1] - ltps[1]) / 2
        print(f"识别到任务交互位置: {left}, {top}")
        left, top = self.assist.window_ops.client_to_screen(self.parent, (left, top))
        print(f"转换为屏幕坐标: {left}, {top}")
        pyautogui.moveTo(left, top)
        return self.output_formater(state=State.FINISHED, reason='数据识别正常', callback=None)
    
    def 任务解析(self, target):
        if target not in self.task_selector:
            return self.output_formater(state=State.FAILED, reason=f'未配置任务[{target}]', callback=None)
        
        tasker = self.task_selector[target]
        print(f"识别到:[{tasker.name}], 点击:[{tasker.click}], 对话:[{tasker.dialogue}], 退出:[{tasker.exit}], 等待:[{tasker.wait}], 战斗:[{tasker.combat}], 操作1:[{tasker.operate}], 操作2:[{tasker.operate2}], 捕捉:[{tasker.catch}], 剧情:[{tasker.plot}]")
        return self.output_formater(state=State.FINISHED, reason='任务解析正常', callback=None, tasker=tasker)

    def 任务检测(self, tasker=None):
        target = '任务目标'
        ltps, rbps = (112, 350), (180, 371)
        region = self.task_region[0], max(self.task_region[1], ltps[1]), self.task_region[2], self.task_region[3]
        results = self.assist.find_color_text_regions(
            region, 
            color_rgb='#ffff00', 
            similarity=0.8
        )
        if not results:
            if not tasker:
                return self.output_formater(state=State.FAILED, reason=f'未识别到[{target}]', callback=None)
            print(f"未识别到[{target}]，继续回调任务检测")
            return self.output_formater(state=State.RUNNING, reason=f'未识别到[{target}]，继续回调任务检测', callback=self.任务检测, tasker=tasker)

        sim, ltps, rbps, text = results[0]
        text = text.rstrip('[')  # 去除末尾的左中括号
        print(f"识别到 [{target}] [{text}] 相似度: {sim} 位置: {ltps}, {rbps}")

        context = self.任务解析(text)
        if context['state'] == State.FAILED:
            return context
        
        tasker = context['kwargs']['tasker']

        return self.通用操作(tasker=tasker)

    def 交互操作(self, tasker=None):
        sim, ltps, rbps, text = self.assist.find_ocr_by_color(
            tasker.dialogue, 
            (377, 517, 1179, 737), 
            color_rgb='#00ff00', 
            similarity=0.8
        )
        if not sim:
            print(f"未识别到[{tasker.dialogue}] 继续回调交互事件")
            time.sleep(1)
            return self.output_formater(state=State.RUNNING, reason='未识别到[{tasker.dialogue}] 继续回调交互事件', callback=self.交互操作, tasker=tasker)
        
        print(f"识别到 [{tasker.dialogue}] 相似度: {sim} 位置: {ltps}, {rbps}")

        left, top = ltps[0] + (rbps[0] - ltps[0]) / 2, ltps[1] + (rbps[1] - ltps[1]) / 2
        left, top = self.assist.window_ops.client_to_screen(self.parent, (left, top))
        self.human_click(left, top)

        # 战斗
        combat = self.combat_selector[tasker.combat]
        # 操作
        operate = self.operate_selector[tasker.operate]
        # 剧情
        plot = self.plot_selector[tasker.plot]

        if any([combat.bool and combat.option == '1', operate.bool and operate.option == '1',]):
            '''战斗或操作'''
            task_funcor = getattr(self, tasker.name, None)
            if not task_funcor:
                return self.output_formater(state=State.FAILED, reason=f'未配置操作[{tasker.name}]', callback=None)
            print(f"执行操作: {task_funcor.__name__}")
            return task_funcor(tasker=tasker)
        elif plot.bool and plot.option == '1':
            '''过场动画，按esc退出'''
            pyautogui.press('esc')
            time.sleep(1)
            return self.output_formater(state=State.RUNNING, reason='开始任务检测', callback=self.任务检测, tasker=tasker)
        elif plot.bool and plot.option == '2':
            '''过场动画，等待结束'''
            return self.output_formater(state=State.RUNNING, reason='等待动画过场', callback=self.任务检测, tasker=tasker)
        else:
            '''操作结束，开启下一个任务'''
            return self.output_formater(state=State.RUNNING, reason='开始任务检测', callback=self.任务检测, tasker=tasker)

    def 通用操作(self, tasker=None):
        sim, ltps, rbps, text = self.assist.find_ocr_by_color(
            tasker.click, 
            self.task_region, 
            color_rgb='#00ff00', 
            similarity=0.8
        )
        if not sim:
            return self.output_formater(state=State.FAILED, reason='未识别到[{tasker.click}]', callback=None)
        
        print(f"识别到 [{tasker.click}] 相似度: {sim} 位置: {ltps}, {rbps}")

        left, top = ltps[0] + (rbps[0] - ltps[0]) / 2, ltps[1] + (rbps[1] - ltps[1]) / 2
        left, top = self.assist.window_ops.client_to_screen(self.parent, (left, top))
        self.human_click(left, top)

        return self.output_formater(state=State.RUNNING, reason='等待交互操作', callback=self.交互操作, tasker=tasker)
    
    def 小试身手(self, tasker=None):
        '''战斗操作，打败猎户'''

    def 南瓜赠礼(self, tasker=None):
        '''第1次选时间宠，第二次重复对话'''

    def 觅得帮手(self, tasker=None):
        '''战斗+捕捉小熊'''

    def 打败南瓜(self, tasker=None):
        '''战斗操作，打败南瓜'''

    def 怒从心起(self, tasker=None):
        '''战斗操作，打败海神'''

    def 驱散鬼气(self, tasker=None):
        '''背包道具操作'''

    def 彼岸寻人(self, tasker=None):
        '''背包道具操作+寻路'''

    def 五族齐心(self, tasker=None):
        '''战斗操作，打败鬼将军'''

    def 再入轮回(self, tasker=None):
        '''背包道具操作'''

    def 神秘封印(self, tasker=None):
        '''战斗操作，可以一直防御'''

    def 交付物资(self, tasker=None):
        '''选择道具点击给予'''

    def 修复项链(self, tasker=None):
        '''背包道具操作，修复项链'''

    def 力挽狂澜(self, tasker=None):
        '''战斗操作，打败鬼将军，组队战斗模式'''

