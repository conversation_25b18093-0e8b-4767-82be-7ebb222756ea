import argparse
import torch
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, Any

from torch_game.core.env import OptimizedMatch3Env
from torch_game.core.agents import PPOAgent
# from torch_game.core.train import Trainer  # TODO: 待实现

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="Train and evaluate a reinforcement learning agent on Match-3 game")
    
    # 环境参数
    parser.add_argument("--board_size", type=int, default=6, help="Size of the game board")
    parser.add_argument("--num_colors", type=int, default=5, help="Number of different colors/symbols")
    
    # 训练参数
    parser.add_argument("--num_episodes", type=int, default=1000, help="Number of training episodes")
    parser.add_argument("--max_steps", type=int, default=100, help="Maximum steps per episode")
    parser.add_argument("--batch_size", type=int, default=64, help="Batch size for training")
    parser.add_argument("--buffer_capacity", type=int, default=10000, help="Capacity of replay buffer")
    parser.add_argument("--update_frequency", type=int, default=4, help="Frequency of agent updates")
    parser.add_argument("--log_frequency", type=int, default=10, help="Frequency of logging")
    
    # 智能体参数
    parser.add_argument("--hidden_dims", type=int, nargs="+", default=[256, 128], help="Hidden dimensions of neural networks")
    parser.add_argument("--lr", type=float, default=3e-4, help="Learning rate")
    parser.add_argument("--gamma", type=float, default=0.99, help="Discount factor")
    parser.add_argument("--gae_lambda", type=float, default=0.95, help="GAE lambda parameter")
    parser.add_argument("--clip_epsilon", type=float, default=0.2, help="PPO clip epsilon")
    
    # 其他参数
    parser.add_argument("--seed", type=int, default=42, help="Random seed")
    parser.add_argument("--save_dir", type=str, default="checkpoints", help="Directory to save models and results")
    parser.add_argument("--render", action="store_true", help="Render the environment during training")
    parser.add_argument("--eval", action="store_true", help="Evaluate the agent after training")
    parser.add_argument("--eval_episodes", type=int, default=10, help="Number of evaluation episodes")
    parser.add_argument("--load_model", type=str, default=None, help="Path to load a pre-trained model")
    
    return parser.parse_args()

def set_seed(seed: int):
    """设置随机种子"""
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)

def main():
    """主函数"""
    # 解析参数
    args = parse_args()
    
    # 设置随机种子
    set_seed(args.seed)
    
    # 创建环境
    env = OptimizedMatch3Env(
        game_assistant=None,  # 需要在实际使用时提供游戏助手
        max_steps=args.max_steps,
        n_colors=args.num_colors,
        storage_capacity=7
    )
    
    # 获取状态和动作空间维度
    state_dim = env.observation_space.shape[0]
    action_dim = env.action_space.n
    
    # 创建智能体
    agent = PPOAgent(
        state_dim=state_dim,
        action_dim=action_dim,
        hidden_dims=tuple(args.hidden_dims),
        lr=args.lr,
        gamma=args.gamma,
        gae_lambda=args.gae_lambda,
        clip_epsilon=args.clip_epsilon
    )
    
    # 如果指定了模型路径，加载预训练模型
    if args.load_model:
        agent.load(args.load_model)
    
    # TODO: 创建训练器 (待实现完整的Trainer类)
    # trainer = Trainer(
    #     env=env,
    #     agent=agent,
    #     buffer_capacity=args.buffer_capacity,
    #     batch_size=args.batch_size,
    #     update_frequency=args.update_frequency,
    #     log_frequency=args.log_frequency,
    #     save_dir=args.save_dir,
    #     render=args.render
    # )

    # 训练智能体
    print("主训练脚本已更新，等待Trainer类实现...")
    print(f"环境: {env}")
    print(f"智能体: {agent}")
    train_results = None  # 临时占位
    # train_results = trainer.train(
    #     num_episodes=args.num_episodes,
    #     max_steps_per_episode=args.max_steps
    # )
    print("等待完整实现后启动训练!")
    
    # 打印训练结果
    print(f"Total training time: {train_results['total_time']:.2f} seconds")
    print(f"Final average reward: {train_results['final_avg_reward']:.2f}")
    print(f"Final average episode length: {train_results['final_avg_length']:.2f}")
    
    # TODO: 评估智能体 (待实现完整的Trainer类)
    if args.eval:
        print("\nStarting evaluation...")
        # eval_results = trainer.evaluate(
        #     num_episodes=args.eval_episodes,
        #     render=args.render
        # )
        eval_results = None  # 临时占位
        print("Evaluation completed!")
        
        # 打印评估结果
        print(f"Evaluation average reward: {eval_results['avg_reward']:.2f}")
        print(f"Evaluation average episode length: {eval_results['avg_length']:.2f}")

if __name__ == "__main__":
    main()