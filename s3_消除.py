import time
from tkinter import N
from PIL import Image
import pyautogui
from collections import defaultdict

from assist import AssistBasicToolkit

from event_bus import event_bus

'''
消除类游戏游戏辅助脚本
工作流程：
1. 通过标题获取窗口句柄，使用部分标题查找，因为不能完全匹配标题
2. 游戏窗口置顶
3. 客户区域截图
4. 使用yolov8进行目标检测
5. 检测到目标后，使用鼠标点击
6. 游戏规则是两个相同的目标消除，所以每次检测到两个目标后，就点击
7. 循环3-6步骤，直到游戏结束
'''

class EliminationGameAssistant:
    def __init__(self):
        self.hwnd = 0

        # assist工具包
        self.assist: AssistBasicToolkit = None

        # assist工具包的操作对象
        self.window_ops = None
        self.screenshot = None
        self.detection = None
        self.image_search = None

        self.is_running = False

        self.game_over_image = None

        # 订阅程序开始事件与结束事件
        event_bus.subscribe('on_executor_started', self._on_executor_started)
        event_bus.subscribe('on_executor_stoped', self._on_executor_stoped)

    @classmethod
    def created(cls, hwnd, assist: AssistBasicToolkit) -> 'EliminationGameAssistant':
        self = cls()
        self.hwnd = hwnd
        self.assist = assist
        self.register_assist_objects()
        return self

    def register_assist_objects(self):
        self.window_ops = self.assist.window_ops
        self.screenshot = self.assist.screenshot
        self.detection = self.assist.detection
        self.image_search = self.assist.image_search

    def _on_executor_started(self, game_over_image):
        '''开始事件回调'''
        if self.is_running:
            return

        self.is_running = True

        self.game_over_image = game_over_image

        self.run()

    def _on_executor_stoped(self):
        '''结束事件回调'''
        if not self.is_running:
            return

        # 释放资源
        self.assist.close()

    def click_targets(self, results):
        """
        检测到两个目标后，点击目标
        """
        targets = defaultdict(list)
        for box in results.boxes:
            cls_id = int(box.cls.item())
            center = ((box.xyxy[0][0]+box.xyxy[0][2])/2, (box.xyxy[0][1]+box.xyxy[0][3])/2)
            targets[cls_id].append(center)

        # 查找可配对目标
        for cls, points in targets.items():
            if len(points) >= 2:
                client_x1, client_y1 = self.window_ops.client_to_screen(
                    self.hwnd, (int(points[0][0]), int(points[0][1]))
                )
                client_x2, client_y2 = self.window_ops.client_to_screen(
                    self.hwnd, (int(points[1][0]), int(points[1][1]))
                )
                pyautogui.click(client_x1, client_y1)
                pyautogui.click(client_x2, client_y2)
                return  # 优先消除最先找到的配对

    def _main_loop(self):
        '''程序操作'''
        # 客户区域截图
        current_img = self.screenshot.capture_client_area()
        # 检测游戏结束目标
        if self.image_search.find_image(current_img, self.game_over_image):
            print("游戏结束")
            self.is_running = False
            return
        # 检测游戏目标
        results = self.detection.detect_targets(current_img)
        # 点击游戏目标
        self.click_targets(results)
        # 等待一段时间，避免过快操作，如果boxes长度大于等于2，说明有两个目标，等待0.1秒，否则等待0.5秒
        num_boxes = len(results.boxes) if hasattr(results, 'boxes') else 0
        time.sleep(0.1 if num_boxes>=2 else 0.5)

    def run(self):
        """
        程序主循环
        """
        try:
            while self.is_running:
                self._main_loop()
        except KeyboardInterrupt:
            print("程序已停止，用户主动操作")
            event_bus.publish('_on_executor_stoped')

if __name__ == "__main__":
    assist = AssistBasicToolkit()
    # 注册并绑定窗口操作对象
    hwnd = assist.register_and_bind_window_objects('最强祖师')
    # 记录模型路径（如assist.detection需指定模型，可在此处传递）
    assist.load_input_detection_model(r"models\zqzs\zqzs.pt")

    # 预存结束画面
    game_over_image = Image.open(r'models\zqzs\点击关闭屏幕.png')

    assistant = EliminationGameAssistant.created(hwnd, assist)

    # 发布开始事件
    event_bus.publish('on_executor_started', game_over_image=game_over_image)


