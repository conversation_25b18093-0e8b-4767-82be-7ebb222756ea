

from utils import Window

# if __name__ == "__main__":
#     window = Window()
#     # hwnd = window.find_window_by_partial_title("按键抓取")
#     # if not hwnd:
#         # print("未找到大话西游2窗口")
#         # exit()

#     print(window.get_window_process(33432712))

import time
from utils import Window

if __name__ == "__main__":
    window = Window()
    hwnd = window.find_window_by_partial_title("大话西游2经典版 $Revision：")
    if not hwnd:
        print("未找到大话西游2窗口")
        exit()

    # child_hwnds = window.enum_child_windows(
    #     hwnd, cls_name="Win32Window", require_visible=False
    # )

    # child = child_hwnds[0][0]

    client_rect = window.get_client_pos(hwnd)
    print(client_rect)

    while True:
        # 获取鼠标当前位置，相对于client_rect的坐标
        mouse_pos = window.get_mouse_pos(hwnd)
        print(mouse_pos)

        # 等待按键退出
        if window.wait_key(hwnd, "q"):
            break

        time.sleep(1)
