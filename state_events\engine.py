'''
1. 获取待操作的角色
2. 获取该角色的当前任务
3. 获取当前任务的状态
4. 根据当前任务的状态，进行任务的处理
5. 处理完任务后，切换到下一个角色
'''

import time
import traceback
import threading

from .characters import Character
from .characters import Characters
from .taskers import Tasker
from .taskers import Taskers
from .state import State
from event_bus import event_bus

from assist import AssistBasicToolkit

import logging
logger = logging.getLogger(__name__)

class Engine:
    
    def __init__(self):
        self.hwnd = 0
        self.assist: AssistBasicToolkit = None

        self.is_running = False
        self.characters: Characters = None
        self.thread = None
        self._stop_event = threading.Event()
        self._last_progress_check = 0  # 上次进度检查时间
        self._progress_check_interval = 5  # 进度检查间隔（秒）

        event_bus.subscribe('on_executor_started', self._on_executor_started)
        event_bus.subscribe('on_executor_stoped', self._on_executor_stoped)
        event_bus.subscribe('on_engine_started', self._on_engine_started)

        # 事件驱动：订阅任务：处理事件/执行事件/处理结果事件
        event_bus.subscribe('on_task_processed', self._on_task_processed)
        event_bus.subscribe('on_task_executed', self._on_task_executed)
        event_bus.subscribe('on_result_processed', self._on_result_processed)
        print('[Engine] 事件驱动已初始化，已订阅 on_state_processed/on_task_executed/on_result_processed')

    @classmethod
    def created(cls, hwnd, assist: AssistBasicToolkit) -> 'Engine':
        self = cls()
        self.hwnd = hwnd
        self.assist = assist
        return self

    def _on_executor_started(self):
        '''开始事件回调'''
        if self.is_running:
            return

        self.is_running = True
        self.thread = threading.Thread(target=self.run, daemon=True)

    def _on_executor_stoped(self):
        '''结束事件回调'''
        if not self.is_running:
            return

        self.is_running = False
        self._check_progress()  # 停止时记录最终进度
        self._stop_event.set()  # 设置停止事件
        if self.is_alive():
            self.thread.join(timeout=5)

        print('[Engine] 引擎终止')

    def _on_engine_started(self, characters: Characters):
        '''引擎启动事件'''
        self.characters = characters
        self.thread.start()

    def _on_task_processed(self, tasker: Tasker, result: dict):
        print(f'[Engine] [processed] [{tasker.hwnd}] [{tasker.name}] tasker.state [{tasker.status}]')
        # 1. 更新角色状态
        character = self.characters.get_character(tasker.hwnd)
        if character:
            character.update_character_state(tasker)
            # print(f'[Engine] [processed] [{character.hwnd}] character.state [{character.status}]')

        # 2. 切换到下一个角色
        next_character = self.characters.next_character()
        if not next_character:
            print('[Engine] [processed] 没有可切换的角色')
            return

        print(f'[Engine] [processed] 窗口切换 from [{character.hwnd}] to [{next_character.hwnd}]')
 
        # 3. 发布执行事件
        event_bus.publish('on_task_executed', character=next_character)

    def _on_task_executed(self, character: Character):
        print(f'[Engine] [executed] [{character.hwnd}] [{character.task_name}] character.state [{character.status}]')
        # 1. 判断角色是否可用
        if not State.is_available(character.status):
            print(f'[Engine] [executed] [{character.hwnd}] 暂时不可用，跳过')
            return

        # 2. 获取角色当前任务
        task_name = character.task_name
        if not task_name:
            print(f"[Engine] [executed] [{character.hwnd}] 无可执行任务")
            return
        
        # 3. 发布任务开始事件
        event_bus.publish('on_tasker_started', hwnd=character.hwnd, name=task_name)
        
        # 4. 切换到该角色窗口
        self.characters.switch_to_window(character)

        # 5. 发布任务执行事件
        event_bus.publish('on_tasker_executed', hwnd=character.hwnd, name=task_name)

    def _on_result_processed(self, tasker: Tasker, result: dict):
        print(f"[Engine] [result] [{tasker.hwnd}] [{tasker.name}] tasker.callback [{tasker._callback}] 执行结束")
        # 根据返回结果更新任务状态
        state = result.get('state')
        reason = result.get('reason')
        callback = result.get('callback')
        args = result.get('args')
        kwargs = result.get('kwargs')

        # 更新任务状态
        tasker.set_tasker_state(state)
        tasker.set_tasker_reason(reason)
        tasker.set_tasker_callback(callback, *args, **kwargs)

        # 事件驱动：不管任务的state是什么，交给事件去处理
        event_bus.publish('on_task_processed', tasker=tasker, result=result)

    def _main_loop(self):
        '''
        主循环负责轮询所有角色，检查并推进其当前任务。
        '''
        try:
            # 事件驱动下，主循环只需保持线程活跃，事件回调驱动主流程
            self._stop_event.wait(timeout=1)
        except KeyboardInterrupt:
            self.is_running = False
        except Exception as e:
            logger.error(f"Error in main loop: {e}")
            logger.error(traceback.format_exc())
            self.is_running = False
            self._stop_event.wait(timeout=1) # 轮询间隔

    def run(self):
        '''
        开始执行任务
        '''
        # 事件驱动：主动触发第一个任务启动
        character = self.characters.next_character()
        if character:
            print(f'[Engine] 事件处理循环启动，触发第一个事件 [{character.hwnd}]')
            event_bus.publish('on_task_executed', character=character)
        else:
            print('[Engine] 启动流程，未找到可用角色窗口')

        while self.is_running and not self._stop_event.is_set():
            self._main_loop()

        print('[Engine] 主循环终止')

    def _check_progress(self):
        """检查并记录所有角色的任务进度"""
        for hwnd, character in self.characters.for_loop_characters():
            if not character:
                continue
            progress_summary = character.get_progress_summary()
            print(f"[Engine] [{character.hwnd}] progress: {progress_summary}")
            
            # 如果所有任务都已完成，记录日志
            if not character.is_all_finished():
                print(f"[Engine] [{character.hwnd}] has not completed all tasks")

    def get_all_progress(self) -> dict:
        """获取所有角色的进度摘要"""
        progress = {}
        for hwnd, character in self.characters.for_loop_characters():
            if not character:
                continue
            progress[hwnd] = character.get_progress_summary()
        return progress
            
    def is_alive(self):
        """检查引擎线程是否存活"""
        return self.thread and self.thread.is_alive()
        




