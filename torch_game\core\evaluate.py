"""
评估脚本
用于评估已训练的强化学习智能体
"""

import argparse
import os
import torch
import numpy as np
import random
import time

from torch_game.env import Match3Env
from torch_game.agents import PPOAgent
from torch_game.utils import Config

def set_seed(seed):
    """设置随机种子"""
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    np.random.seed(seed)
    random.seed(seed)
    torch.backends.cudnn.deterministic = True

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='评估Match-3游戏智能体')
    parser.add_argument('--model', type=str, required=True, help='模型路径')
    parser.add_argument('--config', type=str, default=None, help='配置文件路径')
    parser.add_argument('--seed', type=int, default=42, help='随机种子')
    parser.add_argument('--device', type=str, default='cuda' if torch.cuda.is_available() else 'cpu', 
                        help='评估设备')
    parser.add_argument('--episodes', type=int, default=10, help='评估回合数')
    parser.add_argument('--render', action='store_true', help='是否渲染游戏')
    parser.add_argument('--delay', type=float, default=0.5, help='渲染延迟（秒）')
    args = parser.parse_args()
    
    # 设置随机种子
    set_seed(args.seed)
    
    # 加载配置
    if args.config:
        config = Config.from_file(args.config)
    else:
        config = Config.default_match3_config()
    
    # 创建环境
    env_config = config.get('env', {})
    env = Match3Env(
        board_size=env_config.get('board_size', [8, 8]),
        n_colors=env_config.get('n_colors', 6),
        min_match=env_config.get('min_match', 3),
        max_steps=env_config.get('max_steps', 100)
    )
    
    # 创建智能体
    agent_config = config.get('agent', {})
    state_dim = np.prod(env.observation_space.shape)
    action_dim = env.action_space.n
    
    agent = PPOAgent(
        state_dim=state_dim,
        action_dim=action_dim,
        lr=agent_config.get('lr', 3e-4),
        gamma=agent_config.get('gamma', 0.99),
        epsilon=agent_config.get('epsilon', 0.2),
        value_coef=agent_config.get('value_coef', 0.5),
        entropy_coef=agent_config.get('entropy_coef', 0.01),
        hidden_dim=agent_config.get('hidden_dim', 256),
        device=args.device
    )
    
    # 加载模型
    print(f"加载模型: {args.model}")
    agent.load(args.model)
    agent.eval()
    
    # 评估
    total_rewards = []
    total_steps = []
    
    for episode in range(args.episodes):
        state = env.reset()
        done = False
        episode_reward = 0
        steps = 0
        
        while not done:
            if args.render:
                env.render()
                time.sleep(args.delay)
                
            # 选择动作
            action = agent.select_action(state.reshape(-1), deterministic=True)
            
            # 执行动作
            next_state, reward, done, info = env.step(action)
            episode_reward += reward
            steps += 1
            
            # 更新状态
            state = next_state
            
        total_rewards.append(episode_reward)
        total_steps.append(steps)
        
        print(f"回合 {episode+1}/{args.episodes} - 奖励: {episode_reward:.2f}, 步数: {steps}")
    
    # 打印统计信息
    print("\n评估结果:")
    print(f"平均奖励: {np.mean(total_rewards):.2f} ± {np.std(total_rewards):.2f}")
    print(f"平均步数: {np.mean(total_steps):.2f} ± {np.std(total_steps):.2f}")
    print(f"最高奖励: {np.max(total_rewards):.2f}")
    print(f"最低奖励: {np.min(total_rewards):.2f}")
    
    # 关闭环境
    env.close()

if __name__ == '__main__':
    main()