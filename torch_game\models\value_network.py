import torch
import torch.nn as nn
from typing import Tuple

class ValueNetwork(nn.Module):
    """
    价值网络，用于估计状态值。
    使用一个多层感知机来处理状态输入，输出状态值估计。
    """
    
    def __init__(self, state_dim: int, hidden_dims: Tuple[int, ...] = (256, 128)):
        """
        初始化价值网络。
        
        Args:
            state_dim: 状态空间维度
            hidden_dims: 隐藏层维度的元组，默认为(256, 128)
        """
        super().__init__()
        
        # 构建网络层
        layers = []
        prev_dim = state_dim
        
        # 添加隐藏层
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.ReLU(),
                nn.LayerNorm(hidden_dim),  # 添加层归一化以提高训练稳定性
            ])
            prev_dim = hidden_dim
        
        # 输出层，输出单个值
        layers.append(nn.Linear(prev_dim, 1))
        
        self.network = nn.Sequential(*layers)
    
    def forward(self, state: torch.Tensor) -> torch.Tensor:
        """
        前向传播，计算状态值估计。
        
        Args:
            state: 状态输入张量，形状为(batch_size, state_dim)
            
        Returns:
            torch.Tensor: 状态值估计，形状为(batch_size, 1)
        """
        # 通过网络计算状态值
        value = self.network(state)
        
        return value.squeeze(-1)  # 移除最后一个维度，变为(batch_size,)