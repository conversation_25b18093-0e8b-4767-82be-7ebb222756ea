"""
强化学习智能体基类
定义了所有智能体都需要实现的基本接口
"""

from abc import ABC, abstractmethod
import numpy as np
import torch
import torch.nn as nn

class BaseAgent(ABC):
    """强化学习智能体基类"""
    
    def __init__(self, state_dim, action_dim, device="cuda" if torch.cuda.is_available() else "cpu"):
        """
        初始化智能体
        
        Args:
            state_dim: 状态空间维度
            action_dim: 动作空间维度
            device: 运行设备，默认使用GPU（如果可用）
        """
        self.state_dim = state_dim
        self.action_dim = action_dim
        self.device = device
        
        # 训练相关参数
        self.training = True
        self.current_step = 0
        self.episode = 0
        
    @abstractmethod
    def select_action(self, state, deterministic=False):
        """
        根据状态选择动作
        
        Args:
            state: 当前状态
            deterministic: 是否使用确定性策略（用于评估）
            
        Returns:
            action: 选择的动作
            action_log_prob: 动作的对数概率（可选）
        """
        raise NotImplementedError
        
    @abstractmethod
    def update(self, batch):
        """
        使用一批数据更新智能体
        
        Args:
            batch: 包含transitions的批次数据
            
        Returns:
            info: 包含损失等信息的字典
        """
        raise NotImplementedError
        
    def train(self):
        """切换到训练模式"""
        self.training = True
        
    def eval(self):
        """切换到评估模式"""
        self.training = False
        
    @abstractmethod
    def save(self, path):
        """
        保存模型到指定路径
        
        Args:
            path: 保存路径
        """
        raise NotImplementedError
        
    @abstractmethod
    def load(self, path):
        """
        从指定路径加载模型
        
        Args:
            path: 模型路径
        """
        raise NotImplementedError
        
    def increment_step(self):
        """增加步数计数"""
        self.current_step += 1
        
    def increment_episode(self):
        """增加回合计数"""
        self.episode += 1
        
    def to(self, device):
        """
        将智能体移动到指定设备
        
        Args:
            device: 目标设备
        """
        self.device = device
        return self