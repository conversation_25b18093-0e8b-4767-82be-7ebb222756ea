



from ..state import State
from assist import AssistBasicToolkit

class 街坊任务:
    
    def __init__(self):
        self.hwnd = 0

        self.parent = 0
        self.assist: AssistBasicToolkit = None

        self.total = 0

    @classmethod
    def created(cls, hwnd) -> '街坊任务':
        self = cls()
        self.hwnd = hwnd
        return self

    def register_assist_objects(self, parent, assist: AssistBasicToolkit):
        self.parent = parent
        self.assist = assist
        self.window_ops = self.assist.window_ops
        self.screenshot = self.assist.screenshot
        self.detection = self.assist.detection
        self.image_search = self.assist.image_search

    def start(self):
        # print(f"[{self.__class__.__name__}] start...")
        child, _, _ = self.window_ops.find_child_window(self.hwnd, cls_name="RichEditD2DPT", require_visible=True)
        if not child:
            return {
                'state': State.FAILED,
                'reason': '未找到RichEditD2DPT窗口',
                'callback': None,
                'args': (),
                'kwargs': {}
            }
        
        # 发送消息到RichEditD2DPT窗口
        self.window_ops.send_message(child, f"{self.__class__.__name__} start... \n")
        return {
            'state': State.RUNNING,
            'reason': '',
            'callback': self.run,
            'args': (),
            'kwargs': {}
        }

    def stop(self):
        # print(f"[{self.__class__.__name__}] stop...")
        child, _, _ = self.window_ops.find_child_window(self.hwnd, cls_name="RichEditD2DPT", require_visible=True)
        if not child:
            return {
               'state': State.FAILED,
               'reason': '未找到RichEditD2DPT窗口',
                'callback': None,
                'args': (),
                'kwargs': {}
            }

        # 发送消息到RichEditD2DPT窗口
        self.window_ops.send_message(child, f"{self.__class__.__name__} stop... \n")
        return {
            'state': State.FINISHED,
            'reason': '',
            'callback': None,
            'args': (),
            'kwargs': {}
        }
    
    def run(self):
        # print(f"[{self.__class__.__name__}] run...")
        child, _, _ = self.window_ops.find_child_window(self.hwnd, cls_name="RichEditD2DPT", require_visible=True)
        if not child:
            return {
                'state': State.FAILED,
                'reason': '未找到RichEditD2DPT窗口',
                'callback': None,
                'args': (),
                'kwargs': {}
            }
        
        # 发送消息到RichEditD2DPT窗口
        self.window_ops.send_message(child, f"{self.__class__.__name__} run... \n")

        # 在这里模拟任务进行中，如检测、等待、战斗、寻路等功能
        if self.total < 3:
            self.total += 1
            return {
                'state': State.RUNNING,
                'reason': '任务进行中',
                'callback': self.run,
                'args': (),
                'kwargs': {}
            }
        return {
            'state': State.RUNNING,
            'reason': '任务结束，回调stop',
            'callback': self.stop,
            'args': (),
            'kwargs': {}
        }


