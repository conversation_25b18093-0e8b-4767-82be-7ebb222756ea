"""
测试优化的三消游戏环境
"""

import pytest
import numpy as np
from unittest.mock import <PERSON><PERSON>, MagicMock

from torch_game.core.env import OptimizedMatch3Env


class TestOptimizedMatch3Env:
    """测试优化的三消游戏环境类"""
    
    def setup_method(self):
        """每个测试方法前的设置"""
        # 创建模拟的游戏助手
        self.mock_game_assistant = <PERSON><PERSON>()
        self.mock_game_assistant.ensure_game_ready.return_value = True
        self.mock_game_assistant.screenshot.capture_client_area.return_value = Mock()
        self.mock_game_assistant.detect_current_game_state.return_value = ("in_game", None)
        self.mock_game_assistant.analyze_blocks.return_value = {}
        self.mock_game_assistant.click_block.return_value = None
        
        # 创建环境
        self.env = OptimizedMatch3Env(
            game_assistant=self.mock_game_assistant,
            max_steps=50,
            n_colors=6,
            storage_capacity=7
        )
    
    def test_init(self):
        """测试环境初始化"""
        assert self.env.max_steps == 50
        assert self.env.n_colors == 6
        assert self.env.storage_capacity == 7
        assert self.env.current_step == 0
        assert self.env.total_reward == 0
        
        # 检查观察空间和动作空间
        assert self.env.observation_space.shape[0] > 0
        assert self.env.action_space.n == 100
    
    def test_reset(self):
        """测试环境重置"""
        obs = self.env.reset()
        
        # 检查观察值
        assert isinstance(obs, np.ndarray)
        assert obs.shape == self.env.observation_space.shape
        assert np.all(obs >= 0.0)
        assert np.all(obs <= 1.0)
        
        # 检查环境状态重置
        assert self.env.current_step == 0
        assert self.env.total_reward == 0
        assert self.env._consecutive_failures == 0
    
    def test_step_invalid_action(self):
        """测试无效动作"""
        self.env.reset()
        
        # 模拟无效动作
        obs, reward, done, info = self.env.step(0)
        
        # 检查返回值
        assert isinstance(obs, np.ndarray)
        assert isinstance(reward, (int, float))
        assert isinstance(done, bool)
        assert isinstance(info, dict)
        
        # 检查信息
        assert 'action_success' in info
        assert 'step' in info
        assert 'consecutive_failures' in info
    
    def test_step_valid_action(self):
        """测试有效动作"""
        self.env.reset()
        
        # 模拟有效的方块数据
        mock_blocks = {
            0: [{'class_id': 0, 'position': (100, 100)}],
            1: [{'class_id': 1, 'position': (200, 200)}]
        }
        self.env._get_current_blocks = Mock(return_value=(mock_blocks, {}))
        
        obs, reward, done, info = self.env.step(0)
        
        # 检查返回值
        assert isinstance(obs, np.ndarray)
        assert isinstance(reward, (int, float))
        assert isinstance(done, bool)
        assert isinstance(info, dict)
    
    def test_observation_space(self):
        """测试观察空间"""
        obs = self.env._get_current_observation()
        
        # 检查观察值形状和范围
        assert obs.shape == self.env.observation_space.shape
        assert np.all(obs >= 0.0)
        assert np.all(obs <= 1.0)
    
    def test_episode_done_conditions(self):
        """测试回合结束条件"""
        self.env.reset()
        
        # 测试最大步数限制
        self.env.current_step = self.env.max_steps
        assert self.env._is_episode_done() == True
        
        # 测试连续失败限制
        self.env.current_step = 10
        self.env._consecutive_failures = 10
        assert self.env._is_episode_done() == True
        
        # 测试正常情况
        self.env.current_step = 10
        self.env._consecutive_failures = 5
        assert self.env._is_episode_done() == False
    
    def test_close(self):
        """测试环境关闭"""
        self.env.close()
        assert self.env.game_assistant is None
        assert self.env.last_main_blocks == {}
        assert self.env.last_storage_blocks == {}


class TestOptimizedStateConverter:
    """测试优化的状态转换器"""
    
    def setup_method(self):
        """设置测试"""
        from torch_game.core.env.optimized_match3_env import OptimizedStateConverter
        self.converter = OptimizedStateConverter(n_colors=6, storage_capacity=7)
    
    def test_convert_to_observation(self):
        """测试状态转换"""
        main_blocks = {0: [1, 2], 1: [1]}
        storage_blocks = {0: [1], 2: [1, 2]}
        
        obs = self.converter.convert_to_observation(
            main_blocks, storage_blocks, 
            current_step=10, max_steps=100,
            consecutive_failures=2
        )
        
        # 检查观察值
        assert isinstance(obs, np.ndarray)
        assert obs.dtype == np.float32
        assert np.all(obs >= 0.0)
        assert np.all(obs <= 1.0)


class TestSmartActionMapper:
    """测试智能动作映射器"""
    
    def setup_method(self):
        """设置测试"""
        from torch_game.core.env.optimized_match3_env import SmartActionMapper
        self.mapper = SmartActionMapper()
    
    def test_map_action_to_block(self):
        """测试动作映射"""
        main_blocks = {
            0: [{'class_id': 0, 'position': (100, 100)}],
            1: [{'class_id': 1, 'position': (200, 200)}]
        }
        storage_blocks = {}
        
        # 测试有效动作
        block = self.mapper.map_action_to_block(0, main_blocks, storage_blocks)
        assert block is not None
        assert 'class_id' in block
        assert 'position' in block
        
        # 测试无效动作
        block = self.mapper.map_action_to_block(10, {}, storage_blocks)
        assert block is None


class TestOptimizedRewardCalculator:
    """测试优化的奖励计算器"""
    
    def setup_method(self):
        """设置测试"""
        from torch_game.core.env.optimized_match3_env import OptimizedRewardCalculator
        self.calculator = OptimizedRewardCalculator()
    
    def test_calculate_reward_invalid_action(self):
        """测试无效动作奖励"""
        reward = self.calculator.calculate_reward(
            {}, {}, {}, {},
            action_success=False, action_info={}
        )
        assert reward == -1.0
    
    def test_calculate_reward_elimination(self):
        """测试消除奖励"""
        prev_storage = {0: [1, 2, 3]}  # 3个方块
        new_storage = {}  # 消除了
        
        reward = self.calculator.calculate_reward(
            {}, prev_storage, {}, new_storage,
            action_success=True, action_info={}
        )
        
        # 应该有消除奖励
        assert reward > 0
    
    def test_calculate_reward_storage_penalty(self):
        """测试预存区惩罚"""
        prev_storage = {0: [1, 2, 3, 4, 5]}  # 5个方块
        new_storage = {0: [1, 2, 3, 4, 5, 6, 7]}  # 7个方块（满了）
        
        reward = self.calculator.calculate_reward(
            {}, prev_storage, {}, new_storage,
            action_success=True, action_info={}
        )
        
        # 应该有惩罚
        assert reward < 0
