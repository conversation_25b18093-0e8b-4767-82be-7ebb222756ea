"""
微信OCR基础层模块

该模块提供对微信OCR底层的再次封装，提供基础功能：
1. OCR引擎初始化和管理
2. 图像处理和临时文件管理
3. 基础OCR任务执行和结果处理

该层不直接对外提供服务，而是作为应用层的基础设施。
"""

import os
import cv2
import time
import uuid
import tempfile
import numpy as np
import threading
from PIL import Image
from typing import Union, Dict, Any, Optional, Callable
from wechat_ocr.ocr_manager import OcrManager, OCR_MAX_TASK_ID


class OCRPathManager:
    """OCR路径管理器，负责查找和验证OCR相关路径"""
    
    def __init__(self):
        """初始化路径管理器"""
        self.script_dir = os.path.dirname(os.path.abspath(__file__))
        self.common_paths = os.path.join(self.script_dir, 'path')
        self.ocr_exe_path = os.path.join(self.script_dir, 'path', 'WeChatOCR', 'WeChatOCR.exe')
        
        # 验证路径
        if not self.validate_wechat_path():
            raise FileNotFoundError(f"The path folder does not exist at {self.common_paths}.")
        if not self.find_wechatocr_exe():
            raise FileNotFoundError(f"The WeChatOCR.exe does not exist at {self.ocr_exe_path}.")
    
    def validate_wechat_path(self) -> bool:
        """验证路径是否存在"""
        return os.path.exists(self.common_paths)
    
    def find_wechatocr_exe(self) -> bool:
        """验证OCR可执行文件是否存在"""
        if os.path.isfile(self.ocr_exe_path):
            return True
        else:
            print(f"The WeChatOCR.exe does not exist at {self.ocr_exe_path}.")
            return False


class OCRImageProcessor:
    """OCR图像处理器，负责处理不同类型的图像输入"""

    @staticmethod
    def get_temp_file_path() -> str:
        """获取临时文件路径"""
        # 创建临时文件
        temp_file = f"ocr_temp_{uuid.uuid4().hex}.png"
        temp_dir = tempfile.gettempdir()
        return os.path.join(temp_dir, temp_file)
    
    @staticmethod
    def process_image(img_input: Union[str, np.ndarray, Image.Image]) -> Image.Image:
        """统一输入图像处理，转换为PIL.Image对象"""
        if isinstance(img_input, str):
            if not os.path.exists(img_input):
                raise FileNotFoundError(f"图片路径不存在: {img_input}")
            return Image.open(img_input)
        elif isinstance(img_input, np.ndarray):
            return Image.fromarray(cv2.cvtColor(img_input, cv2.COLOR_BGR2RGB))
        elif isinstance(img_input, Image.Image):
            return img_input
        else:
            raise TypeError("不支持的图像类型，支持类型: str路径、numpy数组、PIL.Image")
    
    @staticmethod
    def save_temp_image(img_input: Union[str, np.ndarray, Image.Image]) -> str:
        """保存临时图片并返回路径"""

        # 获取临时文件路径
        temp_path = OCRImageProcessor.get_temp_file_path()

        # 如果是路径直接返回
        if isinstance(img_input, str):
            if not os.path.exists(img_input):
                raise FileNotFoundError(f"图片路径不存在: {img_input}")

            image = OCRImageProcessor.preprocess_image(cv2.imread(img_input))
            cv2.imwrite(temp_path, image)
            return temp_path
        
        # 处理不同输入类型
        if isinstance(img_input, np.ndarray):
            cv2.imwrite(temp_path, img_input)
        elif isinstance(img_input, Image.Image):
            img_input.save(temp_path)
        else:
            raise TypeError("不支持的图像类型")
            
        return temp_path

    @staticmethod
    def preprocess_image(image):
        """图像预处理函数"""

        # 灰度化
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

        # 二值化
        _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

        # 将二值化图像中的白色和黑色翻转
        inverted = cv2.bitwise_not(binary)

        # 锐化
        kernel = np.array([[0, -1, 0], [-1, 5, -1], [0, -1, 0]])
        sharpened = cv2.filter2D(binary, -1, kernel)

        # 调整对比度和亮度
        alpha = 1.5  # 对比度
        beta = 30    # 亮度
        adjusted = cv2.convertScaleAbs(sharpened, alpha=alpha, beta=beta)

        return adjusted

class OCRResultFormatter:
    """OCR结果格式化器，负责处理和格式化OCR结果"""
    
    @staticmethod
    def format_result(raw_result: Dict[str, Any]) -> list:
        """
        将结果格式化为文本列表
        ocr识别结果示例:
        {'taskId': 3, 'ocrResult': [{'text': '长安城东[50，210]', 'location': {'left': 16.018175, 'top': 17.561493, 'right': 187.0253, 'bottom': 39.515305}, 'pos': {'x': 16.061663, 'y': 17.561493}}]}
        :param raw_result: OCR识别结果字典
        :return: 文本列表
        """
        # 如果ocrResult为空，则输出结果为空列表
        if not raw_result or not raw_result.get('ocrResult'):
            return []
        # 如果ocrResult不为空，则遍历ocrResult中的每个元素，获取text字段的值，转换为列表
        return [item['text'] for item in raw_result['ocrResult'] if item.get('text')]

    @staticmethod
    def format_single_line(raw_result: Dict[str, Any]) -> str:
        """
        将OCR结果中的多行文本合并成一行字符串
        :param raw_result: OCR识别结果字典
        :return: 合并后的单行字符串，如果结果为空则返回空字符串
        """
        # 如果ocrResult为空，则返回空字符串
        if not raw_result or not raw_result.get('ocrResult'):
            return ''
        # 获取所有文本并用空格连接
        return ''.join(item['text'] for item in raw_result['ocrResult'] if item.get('text'))

    @staticmethod
    def format_with_bbox(raw_result: Dict[str, Any]) -> list:
        """
        提取文本及其包围框，返回字典列表
        每项格式：{"text": ..., "bbox": (left, top, right, bottom)}
        """
        if not raw_result or not raw_result.get('ocrResult'):
            return []
        result = []
        for item in raw_result['ocrResult']:
            text = item.get('text', '')
            loc = item.get('location', {})
            bbox = (
                loc.get('left', 0),
                loc.get('top', 0),
                loc.get('right', 0),
                loc.get('bottom', 0)
            )
            result.append({"text": text, "bbox": bbox})
        return result

    @staticmethod
    def format_full(raw_result: Dict[str, Any]) -> list:
        """
        返回原始结构的标准化版本，包含text、bbox、pos等
        每项格式：{"text": ..., "bbox": (left, top, right, bottom), "pos": {"x": ..., "y": ...}}
        """
        if not raw_result or not raw_result.get('ocrResult'):
            return []
        result = []
        for item in raw_result['ocrResult']:
            text = item.get('text', '')
            loc = item.get('location', {})
            bbox = (
                loc.get('left', 0),
                loc.get('top', 0),
                loc.get('right', 0),
                loc.get('bottom', 0)
            )
            pos = item.get('pos', {})
            result.append({"text": text, "bbox": bbox, "pos": pos})
        return result

    @staticmethod
    def find_text_bbox(
        raw_result: Dict[str, Any],
        target: str,
        estimate_inner_bbox: bool = True,
        use_regex: bool = False,
        fuzzy: bool = False
    ) -> list:
        """
        查找目标文字，支持多次出现、模糊匹配、正则查找。
        :param raw_result: OCR识别结果
        :param target: 目标字符串或正则表达式
        :param estimate_inner_bbox: 是否估算目标文字在行内的包围框
        :param use_regex: 是否启用正则查找
        :param fuzzy: 是否启用模糊匹配（target为子串即可）
        :return: [{"text": ..., "bbox": ..., "target_bbox": ..., "target_text": ...}]
        """
        import re
        if not raw_result or not raw_result.get('ocrResult') or not target:
            return []
        result = []
        for item in raw_result['ocrResult']:
            text = item.get('text', '')
            loc = item.get('location', {})
            bbox = (
                loc.get('left', 0),
                loc.get('top', 0),
                loc.get('right', 0),
                loc.get('bottom', 0)
            )
            matches = []
            if use_regex:
                # 正则查找所有匹配
                for m in re.finditer(target, text):
                    matches.append((m.start(), m.end(), m.group()))
            elif fuzzy:
                # 模糊匹配：所有包含target子串的位置
                idx = 0
                while True:
                    idx = text.find(target, idx)
                    if idx == -1:
                        break
                    matches.append((idx, idx + len(target), text[idx:idx+len(target)]))
                    idx += 1  # 支持重叠匹配
            else:
                # 精确匹配，所有出现位置
                idx = 0
                while True:
                    idx = text.find(target, idx)
                    if idx == -1:
                        break
                    matches.append((idx, idx + len(target), text[idx:idx+len(target)]))
                    idx += len(target)
            # 处理所有匹配
            for start_idx, end_idx, target_text in matches:
                if estimate_inner_bbox and len(text) > 0:
                    left, top, right, bottom = bbox
                    width = right - left
                    char_w = width / len(text)
                    target_left = left + char_w * start_idx
                    target_right = left + char_w * end_idx
                    target_bbox = (target_left, top, target_right, bottom)
                else:
                    target_bbox = bbox
                result.append({
                    "text": target_text,
                    "bbox": target_bbox,
                })
        return result

    @staticmethod
    def format_char_bbox(raw_result: Dict[str, Any]) -> list:
        """
        提取每个字及其包围框，返回字典列表
        每项格式：{"char": ..., "bbox": (left, top, right, bottom)}
        """
        if not raw_result or not raw_result.get('ocrResult'):
            return []
        result = []
        for item in raw_result['ocrResult']:
            text = item.get('text', '')
            loc = item.get('location', {})
            text_width = loc.get('right', 0) - loc.get('left', 0)
            char_width = text_width / len(text) if text else 0
            
            for i, char in enumerate(text):
                char_left = loc.get('left', 0) + i * char_width
                char_right = char_left + char_width
                bbox = (
                    char_left,
                    loc.get('top', 0),
                    char_right,
                    loc.get('bottom', 0)
                )
                result.append({"char": char, "bbox": bbox})
        return result


class OCREngine:
    """OCR引擎基础类，提供基础的OCR功能"""

    _instance = None
    _lock = threading.Lock()
    _initialized = False

    def __new__(cls):
        if not cls._instance:
            with cls._lock:
                if not cls._instance:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        """初始化OCR引擎"""
        if not self._initialized:
            self._initialized = True
            self.path_manager = OCRPathManager()
            self.ocr_result = {}
            self.ocr_manager = self._create_ocr_engine()
            print("OCR引擎初始化完成")
    
    def _create_ocr_engine(self) -> OcrManager:
        """初始化OCR引擎核心方法"""
        ocr_manager = OcrManager(self.path_manager.common_paths)
        ocr_manager.SetExePath(self.path_manager.ocr_exe_path)
        ocr_manager.SetUsrLibDir(self.path_manager.common_paths)
        ocr_manager.SetOcrResultCallback(self._result_callback)
        ocr_manager.StartWeChatOCR()
        return ocr_manager
    
    def _result_callback(self, img_path: str, results: Dict[str, Any]):
        """OCR结果回调函数"""
        self.ocr_result = results

    def execute_ocr_task(self, img_path: str) -> Dict[str, Any]:
        """
        执行基础OCR任务
        :param img_path: 图片路径
        :return: OCR识别结果
        """
        # 执行OCR任务
        self.ocr_manager.DoOCRTask(img_path)
        
        # 等待任务完成
        while self.ocr_manager.m_task_id.qsize() != OCR_MAX_TASK_ID:
            pass
            
        return self.ocr_result
    
    def close(self):
        """关闭OCR引擎，释放资源"""
        if hasattr(self.ocr_manager, 'KillWeChatOCR'):
            self.ocr_manager.KillWeChatOCR()
            print("OCR engine closed.")
            self._initialized = False
