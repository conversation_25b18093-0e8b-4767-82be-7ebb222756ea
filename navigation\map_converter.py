'''
地图转换器
'''

import yaml
import random
from typing import <PERSON>ple

from .coord import Coord

class MapConverter:  # 地图转换器类

    def __init__(self):
        self.map_data = self._load_map_config('地图参数.yaml')  # 加载地图配置文件

    def _load_map_config(self, config_file: str) -> dict:
        """加载地图配置文件"""
        with open(config_file, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)  # 读取地图配置文件
        
    def find_target_map(self, map_name: str) ->  dict:
        """根据地图名称查找地图"""
        if map_name in self.map_data:
            return self.map_data[map_name]  # 返回地图信息
        
        raise ValueError(f"地图 {map_name} 未在配置文件中定义")  # 抛出异常

    def _string_rect_trains(self, coord_str: str) -> Tuple[Coord, Coord]:  # 将字符串格式的客户区坐标转换为Coord对象
        """将字符串格式的客户区坐标转换为Coord对象"""
        # coord_str: '((0, 0), (279, 164))'
        clientRect = eval(coord_str)
        return Coord(*clientRect[0]), Coord(*clientRect[1])  # 返回客户区坐标
    
    def _calculate_scale(self, map_min:  Coord, map_max:  Coord, screen_min:  Coord, screen_max:  Coord) -> Coord:
        """计算缩放比例：屏幕差 / 地图差"""
        return (screen_max - screen_min) / (map_max - map_min)  # 返回缩放比例
    
    def _calculate_offset(self, map_min:  Coord, screen_min:  Coord, scale:  Coord) -> Coord:
        """计算偏移量：屏幕坐标 - 地图坐标 * 缩放比例"""
        return screen_min - map_min * scale  # 返回偏移量
    
    def target_map_info(self, name: str) -> Tuple[Coord, Coord]:
        """获取当前地图信息"""
        map_info = self.find_target_map(name)  # 获取地图信息
        map_min, map_max =  self._string_rect_trains(map_info['地图坐标'])  # 获取地图区域
        screen_min, screen_max =  self._string_rect_trains(map_info['屏幕坐标'])  # 获取屏幕区域

        # 计算缩放比例
        scale = self._calculate_scale(map_min, map_max, screen_min, screen_max)
        scale = scale.round(4)
        # 计算偏移量
        offset = self._calculate_offset(map_min, screen_min, scale)
        offset = offset.round(4)

        return  scale, offset  # 返回缩放比例和偏移量

    def map2screen(self, name, map_pos: Coord) -> Coord:
        """将游戏地图坐标转换为屏幕坐标：地图坐标 * 缩放比例 + 偏移量"""

        # 获取当前地图的缩放比例和偏移量
        scale, offset = self.target_map_info(name)

        # 计算转换后的屏幕坐标
        return map_pos * scale + offset

    def screen2map(self, name, screen_pos: Coord) -> Coord:
        """将屏幕坐标转换为游戏地图坐标：(屏幕坐标 - 偏移量) / 缩放比例"""

        # 获取当前地图的缩放比例和偏移量
        scale, offset = self.target_map_info(name)

        # 计算逆变换，将屏幕坐标转换为地图坐标
        return (screen_pos - offset) / scale

    def human_like_offset(self) -> Coord:
        """模拟人类操作偏移模式"""
        # 随机偏移范围，例如-20到20的x和-10到10的y
        x_steps = [0] * 5 + list(range(-20, 21))  # 60%概率无偏移
        y_steps = [0] * 8 + list(range(-10, 11))    # 72%概率无偏移
        return Coord(
            random.choice(x_steps),
            random.choice(y_steps)
        )


        