"""
使用配置文件训练智能体的示例脚本
"""

import os
import argparse
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, Any

from torch_game.core.env import OptimizedMatch3Env
from torch_game.core.agents import PPOAgent
from torch_game.core.train import Trainer
from torch_game.core.utils import ConfigManager

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='Train an agent using configuration file')
    
    parser.add_argument('--config', type=str, default='configs/optimized_game.yaml',
                       help='Path to the configuration file')
    parser.add_argument('--override', type=str, nargs='*', default=[],
                       help='Override config values, format: key=value')
    
    return parser.parse_args()

def override_config(config: ConfigManager, overrides: list) -> None:
    """
    使用命令行参数覆盖配置值
    
    Args:
        config: 配置管理器实例
        overrides: 要覆盖的配置项列表，格式：key=value
    """
    for override in overrides:
        if '=' not in override:
            continue
        key, value = override.split('=', 1)
        
        # 尝试转换为适当的类型
        try:
            # 尝试转换为数字
            if '.' in value:
                value = float(value)
            else:
                value = int(value)
        except ValueError:
            # 如果不是数字，检查是否是布尔值
            if value.lower() == 'true':
                value = True
            elif value.lower() == 'false':
                value = False
            # 否则保持为字符串
        
        # 设置配置值
        config.set(key, value)

def plot_training_results(results: Dict[str, Any], save_path: str = None) -> None:
    """
    绘制训练结果
    
    Args:
        results: 训练结果字典
        save_path: 图表保存路径
    """
    # 提取数据
    episodes = results['episodes']
    rewards = [ep['reward'] for ep in episodes]
    lengths = [ep['length'] for ep in episodes]
    
    # 创建图形
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(10, 8), sharex=True)
    
    # 绘制奖励
    ax1.plot(rewards, 'b-')
    ax1.set_ylabel('Reward')
    ax1.set_title('Training Results')
    ax1.grid(True)
    
    # 绘制回合长度
    ax2.plot(lengths, 'r-')
    ax2.set_xlabel('Episode')
    ax2.set_ylabel('Length')
    ax2.grid(True)
    
    plt.tight_layout()
    
    # 保存图形
    if save_path:
        plt.savefig(save_path)
    
    plt.show()

def main():
    """主函数"""
    # 解析命令行参数
    args = parse_args()
    
    # 加载配置
    config = ConfigManager(args.config)
    
    # 应用命令行覆盖
    override_config(config, args.override)
    
    # 创建保存目录
    save_dir = config.get('training.save_dir', 'checkpoints')
    os.makedirs(save_dir, exist_ok=True)
    
    # 创建日志目录
    log_dir = config.get('logging.log_dir', 'logs')
    if config.get('logging.save_to_file', True):
        os.makedirs(log_dir, exist_ok=True)
    
    # 创建环境
    env = OptimizedMatch3Env(
        game_assistant=None,  # 需要在实际使用时提供游戏助手
        max_steps=config.get('env.max_steps', 150),
        n_colors=config.get('env.n_colors', 19),
        storage_capacity=config.get('env.storage_capacity', 7)
    )
    
    # 计算状态和动作维度
    state_dim = env.observation_space.shape[0]
    action_dim = env.action_space.n
    
    # 创建智能体
    agent = PPOAgent(
        state_dim=state_dim,
        action_dim=action_dim,
        hidden_dims=config.get('agent.hidden_dims', [256, 128]),
        lr=config.get('agent.lr', 3e-4),
        gamma=config.get('agent.gamma', 0.99),
        gae_lambda=config.get('agent.gae_lambda', 0.95),
        clip_epsilon=config.get('agent.clip_epsilon', 0.2)
    )
    
    # 创建训练器
    trainer = Trainer(
        env=env,
        agent=agent,
        buffer_capacity=config.get('training.buffer_capacity', 10000),
        batch_size=config.get('training.batch_size', 64),
        update_frequency=config.get('training.update_frequency', 4),
        log_frequency=config.get('training.log_frequency', 10),
        save_dir=save_dir,
        render=config.get('training.render', False)
    )
    
    # 训练智能体
    print("Training agent...")
    results = trainer.train(
        num_episodes=config.get('training.num_episodes', 1000),
        max_steps_per_episode=config.get('training.max_steps', 100)
    )
    
    # 绘制训练结果
    if config.get('training.plot_results', True):
        plot_path = os.path.join(save_dir, 'training_results.png')
        plot_training_results(results, save_path=plot_path)
    
    # 评估智能体
    print("\nEvaluating agent...")
    eval_results = trainer.evaluate(
        num_episodes=config.get('evaluation.num_episodes', 10),
        render=config.get('evaluation.render', True)
    )
    
    # 打印评估结果
    print(f"\nEvaluation results:")
    print(f"  Average reward: {eval_results['avg_reward']:.2f}")
    print(f"  Average episode length: {eval_results['avg_length']:.2f}")
    print(f"  Total reward: {eval_results['total_reward']:.2f}")
    print(f"  Total episodes: {eval_results['episodes']}")

if __name__ == "__main__":
    main()