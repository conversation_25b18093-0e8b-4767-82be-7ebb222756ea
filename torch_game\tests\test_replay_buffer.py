import unittest
import numpy as np

from torch_game.utils.replay_buffer import ReplayBuffer

class TestReplayBuffer(unittest.TestCase):
    """测试ReplayBuffer类"""
    
    def setUp(self):
        """在每个测试方法之前设置缓冲区"""
        self.capacity = 100
        self.buffer = ReplayBuffer(capacity=self.capacity)
        
        # 创建一些示例数据
        self.state = np.array([1, 2, 3])
        self.action = 1
        self.reward = 1.0
        self.next_state = np.array([4, 5, 6])
        self.done = False
        self.info = {"score": 10}
    
    def test_init(self):
        """测试缓冲区初始化"""
        self.assertEqual(self.buffer.capacity, self.capacity)
        self.assertEqual(len(self.buffer), 0)
        self.assertEqual(self.buffer.position, 0)
    
    def test_add(self):
        """测试添加样本"""
        # 添加一个样本
        self.buffer.add(
            state=self.state,
            action=self.action,
            reward=self.reward,
            next_state=self.next_state,
            done=self.done,
            info=self.info
        )
        
        # 检查缓冲区大小
        self.assertEqual(len(self.buffer), 1)
        
        # 检查存储的数据
        self.assertTrue(np.array_equal(self.buffer.states[0], self.state))
        self.assertEqual(self.buffer.actions[0], self.action)
        self.assertEqual(self.buffer.rewards[0], self.reward)
        self.assertTrue(np.array_equal(self.buffer.next_states[0], self.next_state))
        self.assertEqual(self.buffer.dones[0], self.done)
        self.assertEqual(self.buffer.infos[0], self.info)
    
    def test_add_beyond_capacity(self):
        """测试添加超过容量的样本"""
        # 添加超过容量的样本
        for i in range(self.capacity + 10):
            self.buffer.add(
                state=self.state,
                action=self.action,
                reward=self.reward,
                next_state=self.next_state,
                done=self.done
            )
        
        # 检查缓冲区大小不超过容量
        self.assertEqual(len(self.buffer), self.capacity)
    
    def test_sample(self):
        """测试采样"""
        # 添加多个样本
        for _ in range(50):
            self.buffer.add(
                state=self.state,
                action=self.action,
                reward=self.reward,
                next_state=self.next_state,
                done=self.done
            )
        
        # 采样
        batch_size = 32
        states, actions, rewards, next_states, dones = self.buffer.sample(batch_size)
        
        # 检查采样的批次大小
        self.assertEqual(states.shape[0], batch_size)
        self.assertEqual(actions.shape[0], batch_size)
        self.assertEqual(rewards.shape[0], batch_size)
        self.assertEqual(next_states.shape[0], batch_size)
        self.assertEqual(dones.shape[0], batch_size)
        
        # 检查采样的数据类型
        self.assertEqual(states.dtype, np.ndarray(self.state).dtype)
        self.assertEqual(rewards.dtype, np.float)
        self.assertEqual(dones.dtype, np.bool)
    
    def test_get_all(self):
        """测试获取所有样本"""
        # 添加多个样本
        num_samples = 50
        for _ in range(num_samples):
            self.buffer.add(
                state=self.state,
                action=self.action,
                reward=self.reward,
                next_state=self.next_state,
                done=self.done
            )
        
        # 获取所有样本
        states, actions, rewards, next_states, dones = self.buffer.get_all()
        
        # 检查返回的数据大小
        self.assertEqual(states.shape[0], num_samples)
        self.assertEqual(actions.shape[0], num_samples)
        self.assertEqual(rewards.shape[0], num_samples)
        self.assertEqual(next_states.shape[0], num_samples)
        self.assertEqual(dones.shape[0], num_samples)
    
    def test_reset(self):
        """测试重置缓冲区"""
        # 添加一些样本
        for _ in range(10):
            self.buffer.add(
                state=self.state,
                action=self.action,
                reward=self.reward,
                next_state=self.next_state,
                done=self.done
            )
        
        # 重置缓冲区
        self.buffer.reset()
        
        # 检查缓冲区是否为空
        self.assertEqual(len(self.buffer), 0)
        self.assertEqual(self.buffer.position, 0)
        self.assertEqual(len(self.buffer.states), 0)
        self.assertEqual(len(self.buffer.actions), 0)
        self.assertEqual(len(self.buffer.rewards), 0)
        self.assertEqual(len(self.buffer.next_states), 0)
        self.assertEqual(len(self.buffer.dones), 0)
        self.assertEqual(len(self.buffer.infos), 0)
    
    def test_sample_empty(self):
        """测试从空缓冲区采样"""
        # 从空缓冲区采样应该引发异常
        with self.assertRaises(ValueError):
            self.buffer.sample(32)
    
    def test_sample_batch_size_too_large(self):
        """测试采样批次大小大于缓冲区大小"""
        # 添加一些样本
        for _ in range(10):
            self.buffer.add(
                state=self.state,
                action=self.action,
                reward=self.reward,
                next_state=self.next_state,
                done=self.done
            )
        
        # 采样批次大小大于缓冲区大小应该引发异常
        with self.assertRaises(ValueError):
            self.buffer.sample(20)

if __name__ == '__main__':
    unittest.main()