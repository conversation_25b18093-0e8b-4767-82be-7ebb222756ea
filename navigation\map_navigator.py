'''
地图寻路
'''

import re
import random
import pyautogui
import traceback
from wechatocr import engine

from .coord import Coord
from .map_converter import MapConverter

from utils import Window, ImageController

class MapNavigator:

    def __init__(self, hwnd):
        self.hwnd = hwnd
        # 初始化ocr引擎
        self.ocr = engine()
        # 初始化窗口管理器
        self.window = Window()
        # 初始化图像控制器
        self.graph = ImageController(hwnd)
        # 初始化地图转换器
        self.map_converter = MapConverter()

    def wait_until_arrived(self, target_pos):
        """等待直到到达目标区域"""
        while True:
            # 伪代码：实际需要实现坐标获取逻辑
            current_pos = self._get_current_position()
            if not current_pos:
                print("未获取到当前位置")
                continue

            if self._is_in_target_range(current_pos, target_pos):
                print(f"成功到达目标区域 -> {target_pos}")
                return True

            print(f"当前位置：{current_pos}")
            # 等待一段时间后再次检测
            pyautogui.sleep(1)

    def _get_current_position(self):
        """获取当前游戏内坐标（需根据实际情况实现）"""
        # 截取坐标区域图片
        rect_pos = (35, 90, 245, 145)
        image = self.graph.capture_region(rect_pos)
        # 进行OCR识别
        ocr_result = self.ocr.sync_ocr.recognize(image)
        # 解析OCR结果
        texts = self.ocr.sync_ocr.get_texts(ocr_result)
        if not texts:
            print("未识别到坐标")
            return
        # 解析坐标文本 # 蓬莱·洲[131,46] -> 131,46 使用正则表达式解析
        pattern = r"\[(\d+),(\d+)\]"
        match = re.search(pattern, texts[0])
        if not match:
            print("未识别到坐标")
            return
        x, y = map(int, match.groups())
        # 构建坐标对象
        return Coord(x, y)

    def _is_in_target_range(self, current, target):
        """检测是否到达目标范围"""
        return abs(current.x - target.x) <= 10 and abs(current.y - target.y) <= 10

    def navigate_to(self, name, pos):
        """执行导航到指定坐标"""
        # 1. 目标地图坐标
        target_map_pos = Coord(*pos)
        print(f"目标地图坐标: {target_map_pos}")  # 调试用，打印目标地图坐标，方便检查和调试
        # 2. 目标地图坐标转屏幕坐标
        screen_pos = self.map_converter.map2screen(name, target_map_pos)
        print(f"目标屏幕坐标: {screen_pos}")  # 调试用，打印目标屏幕坐标，方便检查和调试
        # 3. 屏幕随机偏移坐标
        random_screen_pos = self.map_converter.human_like_offset()
        print(f"屏幕随机偏移坐标: {random_screen_pos}")  # 调试用，打印随机偏移坐标，方便检查和调试
        # 4. 计算偏移后的目标屏幕坐标
        screen_pos = screen_pos + random_screen_pos
        print(f"屏幕坐标: {screen_pos}")  # 调试用，打印屏幕坐标，方便检查和调试
        # 5. 限制坐标在窗口范围内
        client_rect = self.window.get_client_pos(self.hwnd)
        print(f"窗口坐标: {client_rect}")  # 调试用，打印窗口坐标，方便检查和调试
        rect_pos = (Coord(*client_rect[:2]), Coord(*client_rect[2:]))
        print(f"窗口坐标范围: {rect_pos}")  # 调试用，打印窗口坐标范围，方便检查和调试
        screen_pos = screen_pos.clamp(*rect_pos)
        print(f"限制后的屏幕坐标: {screen_pos}")  # 调试用，打印限制后的屏幕坐标，方便检查和调试
        # 6. 屏幕坐标转地图坐标
        offset_target_pos = self.map_converter.screen2map(name, screen_pos)
        # 7. 计算偏移后的目标地图坐标
        offset = offset_target_pos - target_map_pos
        offset = offset.truncate(5)  # 确保坐标为整数
        print(f"地图坐标偏移量: {offset}")  # 调试用，打印偏移量，方便检查和调试
        # 8. 鼠标移动到地图坐标，执行点击
        pyautogui.moveTo(screen_pos.x, screen_pos.y, duration=random.uniform(0.5, 1.0))
        pyautogui.click()

        # 9. 等待直到到达目标区域
        self.wait_until_arrived(target_map_pos)

if __name__ == "__main__":
    window = Window()
    hwnd = window.find_window_by_partial_title("大话西游2经典版")
    if not hwnd:
        print("未找到大话西游2窗口")
        exit()
    
    window.set_window_foreground(hwnd)

    navigator = MapNavigator(hwnd)

    try:
        navigator.navigate_to("东海渔村", (100, 100))
    except Exception as e:
        traceback.print_exc()
        print(f"地图导航失败：{e}")
