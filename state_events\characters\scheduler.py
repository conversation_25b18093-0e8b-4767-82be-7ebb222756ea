



from ..state import State
from ..taskers import Tasker

import logging
logger = logging.getLogger(__name__)

class Scheduler:
    '''
    任务调度器
    '''
    def __init__(self):
        self.current_tasker = ''
        self.reason = ''
        self.taskers = []  # 改为队列管理
        self.status = State.PENDING

    def add_tasker(self, task_name):
        if not task_name:
            return
        self.taskers.append(task_name)
        
    def add_taskers(self, task_names):
        if not task_names:
            return

        for task_name in task_names:
            self.add_tasker(task_name)

    def get_next_tasker(self) -> str:
        if not self.taskers:
            return
        return self.taskers.pop(0)

    def set_current_tasker(self, task_name):
        self.current_tasker = task_name

    def get_current_tasker(self) -> str:
        if not self.current_tasker:
            task_name = self.get_next_tasker()
            if not task_name:
                return
            self.set_current_tasker(task_name)
        return self.current_tasker

    def get_character_reason(self):
        return self.reason

    def set_character_reason(self, reason):
        self.reason = reason

    def get_character_state(self):
        return self.status

    def set_character_state(self, state):
        self.status = state

    def update_state(self, tasker: Tasker):
        '''更新角色当前任务信息'''
        if not tasker:
            print(f"Character {self.name}: tasker is None")
            return

        self.set_character_state(tasker.status)
        self.set_character_reason(tasker.reason)

        if not State.is_completed(tasker.status):
            return

        self.set_current_tasker(None)



