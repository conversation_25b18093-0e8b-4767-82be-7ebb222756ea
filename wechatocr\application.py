"""
微信OCR应用层模块

该模块对基础层进行进一步封装，提供三种不同的OCR处理方式：
1. 同步识别功能封装（SyncOCR）- 简单直接的OCR识别
2. 高效任务管理识别功能封装（TaskOCR）- 基于任务队列的批量处理
3. 高性能异步识别功能封装（AsyncOCR）- 基于asyncio的异步处理

每种方式都基于基础层提供的功能，但针对不同的使用场景进行了优化。
"""

import os
import time
import uuid
import queue
import asyncio
import threading
import cv2
import aiofiles
import aiofiles.os as aios
from typing import Union, Dict, List, Any, Optional
import numpy as np
from PIL import Image
from io import BytesIO

from wechatocr.base import (
    OCREngine,
    OCRImageProcessor,
    OCRResultFormatter
)
from wechat_ocr.ocr_manager import OCR_MAX_TASK_ID    


class SyncOCR:
    """
    同步OCR处理类

    提供简单直接的OCR识别功能，适用于单次识别或简单场景。
    特点：
    - 简单易用，直接调用即可获取结果
    - 同步阻塞，适合单次识别
    - 资源占用少，适合轻量级应用
    """

    def __init__(self, engine: OCREngine = None):
        """初始化同步OCR处理器"""
        self.engine = engine or OCREngine()
        self.image_processor = OCRImageProcessor()
        self.result_formatter = OCRResultFormatter()

    def recognize(self, img_input: Union[str, np.ndarray, Image.Image]) -> Dict[str, Any]:
        """
        执行OCR识别
        :param img_input: 支持三种输入类型：
                        - 图片路径 (str)
                        - OpenCV图像 (np.ndarray)
                        - PIL.Image对象
        :return: OCR识别结果字典
        """
        temp_file = None
        try:
            # 生成临时文件路径
            temp_file = self.image_processor.save_temp_image(img_input)

            # 执行OCR任务
            result = self.engine.execute_ocr_task(temp_file)

            return result

        finally:
            # 清理临时文件
            if temp_file and os.path.exists(temp_file) and isinstance(img_input, (np.ndarray, Image.Image)):
                os.remove(temp_file)

    def recognize_roi(self, img_input: Union[str, np.ndarray, Image.Image], roi: tuple) -> Dict[str, Any]:
        """
        识别指定区域
        :param img_input: 图像输入
        :param roi: 感兴趣区域，格式(left, upper, right, lower)
        :return: OCR识别结果
        """
        img = self.image_processor.process_image(img_input)
        cropped = img.crop(roi)
        return self.recognize(cropped)

    def batch_recognize(self, img_list: List[Union[str, np.ndarray, Image.Image]]) -> List[Dict[str, Any]]:
        """
        批量识别多张图片（顺序执行）
        :param img_list: 图像列表
        :return: OCR识别结果列表
        """
        return [self.recognize(img) for img in img_list]

    def format_result(self, raw_result: Dict[str, Any]) -> List[str]:
        """
        格式化OCR结果为文本列表
        :param raw_result: OCR原始结果
        :return: 文本列表
        """
        return self.result_formatter.format_result(raw_result)

    def close(self):
        """关闭OCR引擎，释放资源"""
        self.engine.close()


class TaskManager:
    """
    任务管理器类，用于管理OCR任务

    提供任务队列管理，支持任务的添加、执行和结果获取。
    """

    def __init__(self):
        """初始化任务管理器"""
        self.unstarted_queue = queue.Queue()  # 未开始队列，不限制大小
        self.in_progress = set()  # 进行中任务集合
        self.completed_tasks = {}  # 已完成任务结果字典

        self.task_store = {}  # 任务存储字典，键为任务名称，值为任务数据结构

        # 使用threading.Condition实现精确通知
        self.condition = threading.Condition()

        # 任务状态统计
        self.stats = {
            "unstarted": 0,  # 未开始数量
            "in_progress": 0,  # 进行中数量
            "completed": 0,  # 已完成数量
        }

    def add_task(self, task_name: str, image_path: str) -> str:
        """
        添加任务到未开始队列
        :param task_name: 任务名称
        :param image_path: 图片路径
        :return: 任务名称
        """
        with self.condition:
            task_data = {
                "task_name": task_name,
                "image_path": image_path,
                "status": "unstarted",
                "create_time": time.time()
            }
            self.task_store[task_name] = task_data
            self.unstarted_queue.put(task_name)
            self.stats["unstarted"] += 1
            self.condition.notify_all()

        return task_name

    def start_task(self) -> Optional[Dict[str, Any]]:
        """
        从未开始队列取出任务，添加到进行中集合
        :return: 任务数据
        """
        with self.condition:
            # 任务开始前检查，确保进行中任务数量不超过OCR_MAX_TASK_ID
            while len(self.in_progress) >= OCR_MAX_TASK_ID:
                self.condition.wait()  # 等待任务完成的通知

            if self.unstarted_queue.empty():
                return

            # 从未开始队列中取出任务
            task_name = self.unstarted_queue.get()

            # 更新任务状态
            self.task_store[task_name]["status"] = "in_progress"
            self.in_progress.add(task_name)
            self.stats["unstarted"] -= 1
            self.stats["in_progress"] += 1

        return self.task_store[task_name]

    def complete_task(self, task_name: str, result: Dict[str, Any]) -> str:
        """
        完成任务，存储结果
        :param task_name: 任务名称
        :param result: 任务结果
        :return: 任务名称
        """
        with self.condition:
            if task_name in self.in_progress:
                self.in_progress.remove(task_name)

            # 更新任务状态和结果
            if task_name in self.task_store:
                self.task_store[task_name]["status"] = "completed"
                self.task_store[task_name]["complete_time"] = time.time()

            self.completed_tasks[task_name] = result
            self.stats["in_progress"] -= 1
            self.stats["completed"] += 1

            # 通知等待的线程
            self.condition.notify_all()

        return task_name

    def get_task_result(self, task_name: str) -> Optional[Dict[str, Any]]:
        """
        获取任务结果
        :param task_name: 任务名称
        :return: 任务结果
        """
        return self.completed_tasks.get(task_name)

    def get_task_status(self, task_name: str) -> str:
        """
        获取任务状态
        :param task_name: 任务名称
        :return: 任务状态
        """
        if task_name in self.completed_tasks:
            return "completed"
        elif task_name in self.in_progress:
            return "in_progress"
        elif task_name in self.task_store:
            return "unstarted"
        else:
            return "unknown"

    def wait_for_task_completion(self, task_name: str, timeout: Optional[float] = None) -> bool:
        """
        等待任务完成
        :param task_name: 任务名称
        :param timeout: 超时时间（秒）
        :return: 是否完成
        """
        start_time = time.time()
        while self.get_task_status(task_name) != "completed":
            if timeout and time.time() - start_time > timeout:
                return False
        return True


class TaskOCR:
    """
    高效任务管理OCR处理类

    基于任务队列的批量处理，适用于需要处理多张图片的场景。
    特点：
    - 任务队列管理，支持批量处理
    - 资源复用，提高处理效率
    - 支持任务状态查询和结果获取
    """

    def __init__(self, engine: OCREngine = None):
        """初始化任务管理OCR处理器"""
        self.engine = engine or OCREngine()
        self.image_processor = OCRImageProcessor()
        self.result_formatter = OCRResultFormatter()
        self.task_manager = TaskManager()

        # 启动任务处理线程
        self.stop_event = threading.Event()
        self.worker_thread = threading.Thread(target=self._process_tasks)
        self.worker_thread.daemon = True
        self.worker_thread.start()

    def _process_tasks(self):
        """任务处理线程"""
        while not self.stop_event.is_set():
            task = self.task_manager.start_task()
            if task:
                try:
                    # 执行OCR任务
                    img_path = task["image_path"]
                    result = self.engine.execute_ocr_task(img_path)

                    # 完成任务
                    self.task_manager.complete_task(task["task_name"], result)
                except Exception as e:
                    print(f"Error processing task {task['task_name']}: {e}")
                    # 即使出错也标记为完成，但结果为错误信息
                    self.task_manager.complete_task(task["task_name"], {"error": str(e)})
            else:
                # 没有任务时等待一小段时间
                time.sleep(0.1)

    def recognize(self, img_input: Union[str, np.ndarray, Image.Image]) -> Dict[str, Any]:
        """
        执行OCR识别（任务方式）
        :param img_input: 图像输入
        :return: OCR识别结果
        """
        temp_file = None
        try:
            # 生成临时文件路径
            temp_file = self.image_processor.save_temp_image(img_input)

            # 创建任务
            task_name = os.path.basename(temp_file)
            self.task_manager.add_task(task_name, temp_file)

            # 等待任务完成
            self.task_manager.wait_for_task_completion(task_name)

            # 获取结果
            return self.task_manager.get_task_result(task_name)
        finally:
            # 清理临时文件
            if temp_file and os.path.exists(temp_file) and isinstance(img_input, (np.ndarray, Image.Image)):
                os.remove(temp_file)

    def recognize_async(self, img_input: Union[str, np.ndarray, Image.Image]) -> str:
        """
        异步提交OCR识别任务，返回任务ID
        :param img_input: 图像输入
        :return: 任务ID
        """
        # 生成临时文件路径
        temp_file = self.image_processor.save_temp_image(img_input)

        # 创建任务
        task_name = os.path.basename(temp_file)
        self.task_manager.add_task(task_name, temp_file)

        return task_name

    def get_result(self, task_name: str, wait: bool = True, timeout: Optional[float] = None) -> Optional[Dict[str, Any]]:
        """
        获取任务结果
        :param task_name: 任务名称
        :param wait: 是否等待任务完成
        :param timeout: 等待超时时间
        :return: 任务结果
        """
        if wait:
            self.task_manager.wait_for_task_completion(task_name, timeout)

        return self.task_manager.get_task_result(task_name)

    def batch_recognize(self, img_list: List[Union[str, np.ndarray, Image.Image]]) -> List[Dict[str, Any]]:
        """
        批量识别多张图片
        :param img_list: 图像列表
        :return: OCR识别结果列表
        """
        # 提交所有任务
        task_names = []
        temp_files = []

        for img in img_list:
            temp_file = self.image_processor.save_temp_image(img)
            temp_files.append((temp_file, isinstance(img, (np.ndarray, Image.Image))))

            task_name = os.path.basename(temp_file)
            self.task_manager.add_task(task_name, temp_file)
            task_names.append(task_name)

        # 等待所有任务完成
        for task_name in task_names:
            self.task_manager.wait_for_task_completion(task_name)

        # 获取所有结果
        results = [self.task_manager.get_task_result(task_name) for task_name in task_names]

        # 清理临时文件
        for temp_file, should_delete in temp_files:
            if should_delete and os.path.exists(temp_file):
                os.remove(temp_file)

        return results

    def format_result(self, raw_result: Dict[str, Any]) -> List[str]:
        """
        格式化OCR结果为文本列表
        :param raw_result: OCR原始结果
        :return: 文本列表
        """
        return self.result_formatter.format_result(raw_result)

    def close(self):
        """关闭OCR引擎，释放资源"""
        self.stop_event.set()
        self.worker_thread.join(timeout=2.0)
        self.engine.close()


class AsyncOCR:
    """
    高性能异步OCR处理类

    基于asyncio的异步处理，适用于高并发场景。
    特点：
    - 异步处理，不阻塞主线程
    - 高并发支持，适合大量图片处理
    - 资源高效利用
    """

    def __init__(self, engine: OCREngine = None):
        """初始化异步OCR处理器"""
        self.engine = engine or OCREngine()
        self.image_processor = OCRImageProcessor()
        self.result_formatter = OCRResultFormatter()

        # 异步任务管理
        self.tasks = {}
        self.results = {}
        self.lock = threading.Lock()

        # 为每个实例创建独立事件循环和运行线程
        self.loop = asyncio.new_event_loop()
        self.thread = threading.Thread(
            target=self._run_loop, 
            daemon=True,
            name=f"AsyncOCRLoop-{id(self)}"
        )
        self.thread.start()
        time.sleep(0.5)

    def _run_loop(self):
        """事件循环运行线程"""
        asyncio.set_event_loop(self.loop)
        self.loop.run_forever()

    async def _save_temp_image_async(self, img_input: Union[str, np.ndarray, Image.Image]) -> str:
        """异步保存临时图片"""

        # 获取临时文件路径
        temp_file = self.image_processor.get_temp_file_path()

        # 如果是路径直接返回
        if isinstance(img_input, str):
            if not await aios.path.exists(img_input):
                raise FileNotFoundError(f"图片路径不存在: {img_input}")

            image = self.image_processor.preprocess_image(cv2.imread(img_input))
            _, buffer = cv2.imencode('.png', image)
            async with aiofiles.open(temp_file, 'wb') as f:
                await f.write(image.tobytes())
            return temp_file

        try:
            # 处理不同输入类型
            if isinstance(img_input, np.ndarray):
                # 使用OpenCV保存图像到内存
                _, buffer = cv2.imencode('.png', img_input)
                async with aiofiles.open(temp_file, 'wb') as f:
                    await f.write(buffer.tobytes())
            elif isinstance(img_input, Image.Image):
                # 使用PIL保存图像到内存
                img_byte_arr = BytesIO()
                img_input.save(img_byte_arr, format='PNG')
                async with aiofiles.open(temp_file, 'wb') as f:
                    await f.write(img_byte_arr.getvalue())
            else:
                raise TypeError("不支持的图像类型")

            return temp_file
        except Exception as e:
            if await aios.path.exists(temp_file):
                await aios.remove(temp_file)
            raise e

    async def recognize(self, img_input: Union[str, np.ndarray, Image.Image]) -> str:
        """异步识别（线程安全版本）"""
        # 将协程提交到专属事件循环
        fut = asyncio.run_coroutine_threadsafe(
            self._async_recognize(img_input),
            self.loop
        )
        return fut.result()

    async def _async_recognize(self, img_input: Union[str, np.ndarray, Image.Image]) -> Dict[str, Any]:
        """
        异步执行OCR识别
        :param img_input: 图像输入
        :return: OCR识别结果
        """
        temp_file = None
        try:
            # 异步保存临时文件
            temp_file = await self._save_temp_image_async(img_input)

            # 使用线程池执行同步OCR任务
            loop = asyncio.get_running_loop()
            result = await loop.run_in_executor(
                None,
                self.engine.execute_ocr_task,
                temp_file
            )

            return result
        finally:
            # 异步清理临时文件
            if temp_file and await aios.path.exists(temp_file) and isinstance(img_input, (np.ndarray, Image.Image)):
                await aios.remove(temp_file)

    async def batch_recognize(self, img_list: List[Union[str, np.ndarray, Image.Image]]) -> List[Dict[str, Any]]:
        """
        异步批量识别多张图片
        :param img_list: 图像列表
        :return: OCR识别结果列表
        """
        tasks = [self.recognize(img) for img in img_list]
        return await asyncio.gather(*tasks)

    def format_result(self, raw_result: Dict[str, Any]) -> List[str]:
        """
        格式化OCR结果为文本列表
        :param raw_result: OCR原始结果
        :return: 文本列表
        """
        return self.result_formatter.format_result(raw_result)

    def close(self):
        """关闭OCR引擎，释放资源"""
        self.loop.call_soon_threadsafe(self.loop.stop)
        if self.thread.is_alive():
            self.thread.join(timeout=2.0)
        self.engine.close()
