import win32gui
import win32ui
import win32con
import win32api
import win32process
import win32clipboard
import keyboard
import time
import os
import threading
import ctypes
from PIL import Image

from utils import Window

class CaptureTool:
    def __init__(self, target_hwnd):
        self.target_hwnd = target_hwnd
        self.selecting = False
        self.start_pos = None
        self.end_pos = None
        self.last_rect = None
        self.overlay_hwnd = None
        self.hook_thread = None
        self.overlay_thread = None
        self.overlay_done = False
        self.overlay_exited = False
        
        # 全局字典用于存储工具实例
        if not hasattr(CaptureTool, 'tool_map'):
            CaptureTool.tool_map = {}
        self.tool_map = CaptureTool.tool_map
        
    def start_selection(self):
        """激活目标窗口并进入选择模式"""
        if self.selecting:
            return
        
        self.selecting = True
        self.overlay_done = False
        self.overlay_exited = False
        
        # 设置目标窗口为前台窗口
        win32gui.SetForegroundWindow(self.target_hwnd)
        time.sleep(0.1)  # 等待窗口激活
        
        # 在独立线程中创建覆盖窗口
        self.overlay_thread = threading.Thread(target=self._create_overlay)
        self.overlay_thread.daemon = True
        self.overlay_thread.start()
        
        # 在独立线程中监听键盘输入
        self.hook_thread = threading.Thread(target=self._input_listener)
        self.hook_thread.daemon = True
        self.hook_thread.start()
        
        print("选择模式已激活，使用鼠标选择区域后按Enter确认或ESC取消")
        
    def _create_overlay(self):
        """创建透明覆盖窗口用于绘制选择框"""
        try:
            # 获取目标窗口的尺寸
            client_rect = win32gui.GetClientRect(self.target_hwnd)
            self.width = client_rect[2]
            self.height = client_rect[3]
            
            # 获取客户区在屏幕上的位置
            point_top = win32gui.ClientToScreen(self.target_hwnd, (0, 0))
            point_bottom = win32gui.ClientToScreen(self.target_hwnd, (self.width, self.height))
            screen_rect = (point_top[0], point_top[1], point_bottom[0], point_bottom[1])
            
            print(f"正在创建窗口，位置: {screen_rect}")
            
            # 注册窗口类
            class_name = "ScreenshotOverlay_" + str(time.time_ns())
            wc = win32gui.WNDCLASS()
            wc.lpfnWndProc = self._overlay_wnd_proc
            wc.hInstance = win32api.GetModuleHandle(None)
            wc.lpszClassName = class_name
            wc.hCursor = win32gui.LoadCursor(0, win32con.IDC_CROSS)
            wc.hbrBackground = win32gui.GetStockObject(win32con.WHITE_BRUSH)
            wc.style = win32con.CS_HREDRAW | win32con.CS_VREDRAW
            
            try:
                win32gui.RegisterClass(wc)
            except Exception as e:
                print(f"注册窗口类失败: {e}")
                self.selecting = False
                return
            
            # 创建透明覆盖窗口
            style = win32con.WS_POPUP | win32con.WS_VISIBLE
            ex_style = win32con.WS_EX_LAYERED | win32con.WS_EX_TOPMOST | win32con.WS_EX_TOOLWINDOW
            
            try:
                self.overlay_hwnd = win32gui.CreateWindowEx(
                    ex_style,
                    class_name,
                    "Screenshot Overlay",
                    style,
                    screen_rect[0],
                    screen_rect[1],
                    screen_rect[2] - screen_rect[0],
                    screen_rect[3] - screen_rect[1],
                    0, 0, wc.hInstance, None
                )
            except Exception as e:
                print(f"创建窗口失败: {e}")
                self.selecting = False
                return
                
            if not self.overlay_hwnd:
                print("创建窗口失败")
                self.selecting = False
                return
                
            print(f"窗口创建成功，句柄: {self.overlay_hwnd}")
            
            # 设置窗口为半透明
            win32gui.SetLayeredWindowAttributes(self.overlay_hwnd, 0, 128, win32con.LWA_ALPHA)
            
            # 将工具实例与窗口句柄关联
            self.tool_map[self.overlay_hwnd] = self
            
            # 设置窗口为前台
            try:
                # 获取当前线程ID
                current_thread = win32api.GetCurrentThreadId()
                # 获取前台窗口线程ID
                foreground_window = win32gui.GetForegroundWindow()
                foreground_thread = win32process.GetWindowThreadProcessId(foreground_window)[0]
                
                # 尝试将窗口设为前台
                if foreground_thread != current_thread:
                    try:
                        win32process.AttachThreadInput(current_thread, foreground_thread, True)
                        win32gui.BringWindowToTop(self.overlay_hwnd)
                        win32gui.ShowWindow(self.overlay_hwnd, win32con.SW_SHOWNORMAL)
                        win32gui.UpdateWindow(self.overlay_hwnd)
                        win32process.AttachThreadInput(current_thread, foreground_thread, False)
                    except:
                        # 如果线程关联失败，尝试其他方法
                        win32gui.ShowWindow(self.overlay_hwnd, win32con.SW_SHOWNORMAL)
                        win32gui.BringWindowToTop(self.overlay_hwnd)
                else:
                    win32gui.ShowWindow(self.overlay_hwnd, win32con.SW_SHOWNORMAL)
                    win32gui.BringWindowToTop(self.overlay_hwnd)
                    
                print("窗口已显示")
                    
            except Exception as e:
                print(f"设置窗口前台失败: {e}")
                # 继续执行，因为窗口仍然可以工作
            
            print("正在进入消息循环")
            
            # 消息循环
            while self.selecting:
                try:
                    # 使用GetMessage处理消息，这个实现更稳定
                    result, msg = win32gui.GetMessage(None, 0, 0)
                    if result <= 0:  # 错误或WM_QUIT
                        break
                        
                    win32gui.TranslateMessage(msg)
                    win32gui.DispatchMessage(msg)
                    
                    if not win32gui.IsWindow(self.overlay_hwnd):
                        break
                        
                except Exception as e:
                    print(f"消息处理错误: {e}")
                    break
            
        except Exception as e:
            print(f"创建覆盖窗口过程中出错: {str(e)}")
            self.selecting = False
    
    @staticmethod
    def _overlay_wnd_proc(hwnd, msg, wParam, lParam):
        """覆盖窗口的消息处理函数"""
        try:
            # 从全局字典获取工具实例
            tool = None
            if hasattr(CaptureTool, 'tool_map'):
                tool = CaptureTool.tool_map.get(hwnd)
            
            if not tool:
                return win32gui.DefWindowProc(hwnd, msg, wParam, lParam)
            
            if msg == win32con.WM_CLOSE:
                win32gui.DestroyWindow(hwnd)
                return 0
                
            elif msg == win32con.WM_DESTROY:
                if hwnd in CaptureTool.tool_map:
                    CaptureTool.tool_map.pop(hwnd)
                win32gui.PostQuitMessage(0)
                return 0
            elif msg == win32con.WM_LBUTTONDOWN:
                # 记录鼠标按下的位置作为起始点
                x, y = win32api.LOWORD(lParam), win32api.HIWORD(lParam)
                tool.start_pos = (x, y)
                # 初始化结束点与起始点相同，这样即使只点击也能形成一个点
                tool.end_pos = (x, y)
                win32gui.SetCapture(hwnd)  # 捕获鼠标以接收鼠标移动和释放消息
                # 立即绘制初始点
                tool._draw_selection()
                return 0
                
            elif msg == win32con.WM_MOUSEMOVE:
                if tool.start_pos and win32gui.GetCapture() == hwnd:  # 确保我们仍然捕获了鼠标
                    x, y = win32api.LOWORD(lParam), win32api.HIWORD(lParam)
                    # 更新结束点为当前鼠标位置并立即重绘
                    if (x, y) != tool.end_pos:  # 只有在位置真正改变时才重绘
                        tool.end_pos = (x, y)
                        tool._draw_selection()
                return 0
                
            elif msg == win32con.WM_LBUTTONUP:
                if tool.start_pos and win32gui.GetCapture() == hwnd:
                    x, y = win32api.LOWORD(lParam), win32api.HIWORD(lParam)
                    tool.end_pos = (x, y)
                    win32gui.ReleaseCapture()  # 释放鼠标捕获
                    # 最后一次绘制选择框
                    tool._draw_selection()
                return 0
                
            elif msg == win32con.WM_PAINT:
                # 简化WM_PAINT处理，使用GetDC而不是BeginPaint/EndPaint
                hdc = win32gui.GetDC(hwnd)
                if tool.last_rect:
                    tool._draw_selection()  # 重新绘制选择框
                win32gui.ReleaseDC(hwnd, hdc)
                return 0
                
        except Exception as e:
            print(f"窗口过程错误: {e}")
            
        return win32gui.DefWindowProc(hwnd, msg, wParam, lParam)    
    
    def _draw_selection(self):
        """绘制选择矩形，使用更优化的绘制方式减少闪烁"""
        if not self.overlay_hwnd or not self.start_pos or not self.end_pos:
            return
            
        try:
            # 确保所有坐标有效且在窗口范围内
            def clamp(val, min_val, max_val):
                return max(min_val, min(val, max_val))
            
            # 获取窗口尺寸
            rect = win32gui.GetClientRect(self.overlay_hwnd)
            max_width = rect[2]
            max_height = rect[3]
            
            # 规范化坐标确保在窗口范围内
            x1 = clamp(self.start_pos[0], 0, max_width)
            y1 = clamp(self.start_pos[1], 0, max_height)
            x2 = clamp(self.end_pos[0], 0, max_width)
            y2 = clamp(self.end_pos[1], 0, max_height)
            
            # 计算矩形坐标（支持从任意方向选择）
            left = min(x1, x2)
            top = min(y1, y2)
            right = max(x1, x2)
            bottom = max(y1, y2)            # 获取设备上下文
            hdc = win32gui.GetDC(self.overlay_hwnd)

            try:
                # 设置背景模式为透明
                win32gui.SetBkMode(hdc, win32con.TRANSPARENT)
                
                # 创建空画刷（完全透明）
                null_brush = win32gui.GetStockObject(win32con.NULL_BRUSH)
                old_brush = win32gui.SelectObject(hdc, null_brush)
                
                # 创建绿色画笔
                pen = win32gui.CreatePen(win32con.PS_SOLID, 2, 0x00FF00)  # RGB(0,255,0) 绿色
                old_pen = win32gui.SelectObject(hdc, pen)
                
                try:
                    # 设置绘制模式为异或模式，这样可以通过重绘来擦除旧的矩形
                    win32gui.SetROP2(hdc, win32con.R2_XORPEN)
                    
                    # 先擦除旧矩形（如果存在）
                    if self.last_rect:
                        win32gui.Rectangle(hdc, *self.last_rect)
                    
                    # 绘制新矩形
                    win32gui.Rectangle(hdc, left, top, right, bottom)
                    
                    # 保存当前矩形（使用原始坐标）
                    self.last_rect = (left, top, right, bottom)
                    
                finally:
                    # 恢复原始画笔和画刷
                    if old_pen:
                        win32gui.SelectObject(hdc, old_pen)
                    if pen:
                        win32gui.DeleteObject(pen)
                    if old_brush:
                        win32gui.SelectObject(hdc, old_brush)
                    # null_brush是系统对象，不需要删除
            finally:
                # 释放设备上下文
                if hdc:
                    win32gui.ReleaseDC(self.overlay_hwnd, hdc)
            
        except Exception as e:
            print(f"绘制选择框时出错: {str(e)}")
            self.last_rect = None
            
    def _input_listener(self):
        """监听键盘输入"""
        while self.selecting and not self.overlay_exited:
            if keyboard.is_pressed('enter'):
                self._confirm_selection()
                break
            elif keyboard.is_pressed('esc'):
                self._cancel_selection()
                break
            time.sleep(0.05)
    
    def _confirm_selection(self):
        """确认选择并截图"""
        if not self.start_pos or not self.end_pos:
            print("未选择区域")
            self._cleanup()
            return
            
        left = min(self.start_pos[0], self.end_pos[0])
        top = min(self.start_pos[1], self.end_pos[1])
        right = max(self.start_pos[0], self.end_pos[0])
        bottom = max(self.start_pos[1], self.end_pos[1])
        
        # 复制坐标到剪贴板
        coord_text = f"({left}, {top}, {right}, {bottom})"
        try:
            win32clipboard.OpenClipboard()
            win32clipboard.EmptyClipboard()
            win32clipboard.SetClipboardText(coord_text)
            win32clipboard.CloseClipboard()
            print(f"坐标已复制到剪贴板: {coord_text}")
        except Exception as e:
            print(f"复制到剪贴板失败: {str(e)}")
        
        # 确保选择区域有效
        if left >= right or top >= bottom:
            print("选择区域无效")
            self._cleanup()
            return
        
        # 确保目标窗口仍然有效
        if not win32gui.IsWindow(self.target_hwnd):
            print("目标窗口已关闭")
            self._cleanup()
            return
            
        print(f"准备截图区域: ({left}, {top}, {right}, {bottom})")
        
        # 捕获整个客户区
        full_capture = self._capture_client_area()
        
        if not full_capture:
            print("截图失败")
            self._cleanup()
            return
        
        try:
            # 裁剪选择区域
            cropped_img = full_capture.crop((left, top, right, bottom))
            
            # 保存截图
            temp_dir = os.path.join(os.getcwd(), 'temp')  # 改为保存到temp目录
            if not os.path.exists(temp_dir):
                os.makedirs(temp_dir)
                
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            filename = os.path.join(temp_dir, f"screenshot_{timestamp}.png")
            cropped_img.save(filename)
            
            print(f"截图已保存: {filename}")
            print(f"坐标位置: Left={left}, Top={top}, Right={right}, Bottom={bottom}")
            
        except Exception as e:
            print(f"保存截图失败: {str(e)}")
            
        self._cleanup()
    
    def _cancel_selection(self):
        """取消选择模式"""
        print("截图已取消")
        self._cleanup()
    
    def _cleanup(self):
        """清理资源"""
        self.overlay_done = True
        self.selecting = False
        self.overlay_exited = True
        
        # 销毁覆盖窗口
        if self.overlay_hwnd and win32gui.IsWindow(self.overlay_hwnd):
            try:
                # 发送销毁消息
                win32gui.PostMessage(self.overlay_hwnd, win32con.WM_CLOSE, 0, 0)
                # 等待窗口销毁
                for _ in range(10):
                    if not win32gui.IsWindow(self.overlay_hwnd):
                        break
                    time.sleep(0.1)
            except:
                pass
        
        # 从全局字典中移除
        if self.overlay_hwnd in CaptureTool.tool_map:
            CaptureTool.tool_map.pop(self.overlay_hwnd, None)
            
        self.overlay_hwnd = None
        self.start_pos = None
        self.end_pos = None
        self.last_rect = None
        
        # 等待线程结束，但避免等待当前线程
        current_thread = threading.current_thread()
        if self.hook_thread and self.hook_thread.is_alive() and self.hook_thread != current_thread:
            self.hook_thread.join(timeout=1.0)
        if self.overlay_thread and self.overlay_thread.is_alive() and self.overlay_thread != current_thread:
            self.overlay_thread.join(timeout=1.0)
    
    def _capture_client_area(self):
        """截图整个窗口客户区"""
        hwndDC = None
        mfcDC = None
        saveDC = None
        saveBitMap = None
        result = None

        try:
            # 获取窗口信息
            if not win32gui.IsWindow(self.target_hwnd):
                print("目标窗口无效")
                return None
            
            # 获取客户区大小
            client_rect = win32gui.GetClientRect(self.target_hwnd)
            width = client_rect[2]
            height = client_rect[3]
            
            print(f"正在截图，窗口大小: {width}x{height}")
            
            # 创建设备上下文
            hwndDC = win32gui.GetDC(self.target_hwnd)
            if not hwndDC:
                print("获取窗口DC失败")
                return None
                
            mfcDC = win32ui.CreateDCFromHandle(hwndDC)
            saveDC = mfcDC.CreateCompatibleDC()
            
            # 创建位图对象
            saveBitMap = win32ui.CreateBitmap()
            saveBitMap.CreateCompatibleBitmap(mfcDC, width, height)
            
            # 选择位图对象
            old_bitmap = saveDC.SelectObject(saveBitMap)
            
            # 复制窗口内容
            print("开始复制窗口内容...")
            saveDC.BitBlt((0, 0), (width, height), mfcDC, (0, 0), win32con.SRCCOPY)
            print("复制窗口内容成功")
            
            # 获取位图信息和数据
            bmpinfo = saveBitMap.GetInfo()
            bmpstr = saveBitMap.GetBitmapBits(True)
            
            # 转换为PIL图像
            result = Image.frombuffer(
                'RGB',
                (bmpinfo['bmWidth'], bmpinfo['bmHeight']),
                bmpstr, 'raw', 'BGRX', 0, 1
            )

            # 还原原始位图
            saveDC.SelectObject(old_bitmap)
            
            return result
            
        except Exception as e:
            print(f"截图过程出错: {str(e)}")
            return None
            
        finally:
            # 按照正确的顺序清理资源
            if saveBitMap:
                try:
                    win32gui.DeleteObject(saveBitMap.GetHandle())
                except:
                    pass
            if saveDC:
                try:
                    saveDC.DeleteDC()
                except:
                    pass
            if mfcDC:
                try:
                    mfcDC.DeleteDC()
                except:
                    pass
            if hwndDC:
                try:
                    win32gui.ReleaseDC(self.target_hwnd, hwndDC)
                except:
                    pass

def get_window_by_title(title):
    """通过窗口标题获取句柄"""
    return win32gui.FindWindow(None, title)

# 使用示例
if __name__ == "__main__":
    w = Window()
    # 获取目标窗口句柄 (修改为你的窗口标题)
    window_title = "大话西游2经典版 $Revision："  # 示例目标窗口
    target_hwnd = w.find_window_by_partial_title(window_title)
    
    if not target_hwnd:
        print(f"未找到窗口: {window_title}")
        exit()
    
    # 初始化截图工具
    tool = CaptureTool(target_hwnd)
    
    # 注册全局热键
    keyboard.add_hotkey('ctrl+1', tool.start_selection)
    
    print("截图工具已启动! 按下 Ctrl+1 开始选择截图区域")
    print("使用鼠标在目标窗口中拖动选择区域，按Enter确认，ESC取消")
    
    # 保持程序运行
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("程序已退出")
