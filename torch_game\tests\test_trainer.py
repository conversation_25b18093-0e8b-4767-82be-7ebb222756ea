import unittest
import numpy as np
from unittest.mock import MagicMock, patch

from torch_game.utils.trainer import Trainer
from torch_game.env.base_env import BaseEnv
from torch_game.agents.base_agent import BaseAgent

class MockEnv(BaseEnv):
    """模拟环境类"""
    
    def __init__(self):
        self.observation_space = MagicMock()
        self.observation_space.shape = (10,)
        self.action_space = MagicMock()
        self.action_space.n = 4
        self.reset_called = 0
        self.step_called = 0
        self.render_called = 0
    
    def reset(self):
        self.reset_called += 1
        return np.zeros(10)
    
    def step(self, action):
        self.step_called += 1
        # 返回 (next_state, reward, done, info)
        return np.ones(10), 1.0, self.step_called >= 10, {"score": self.step_called}
    
    def render(self):
        self.render_called += 1

class MockAgent(BaseAgent):
    """模拟智能体类"""
    
    def __init__(self):
        self.select_action_called = 0
        self.update_called = 0
        self.save_called = 0
    
    def select_action(self, state):
        self.select_action_called += 1
        return 0, {"log_prob": 0.0, "value": 0.0}
    
    def update(self, states, actions, rewards, next_states, dones):
        self.update_called += 1
        return {"policy_loss": 0.1, "value_loss": 0.2, "entropy": 0.3}
    
    def save(self, path):
        self.save_called += 1

class TestTrainer(unittest.TestCase):
    """测试Trainer类"""
    
    def setUp(self):
        """在每个测试方法之前设置训练器"""
        self.env = MockEnv()
        self.agent = MockAgent()
        self.buffer_capacity = 100
        self.batch_size = 32
        self.update_frequency = 4
        self.log_frequency = 10
        self.save_dir = "test_checkpoints"
        self.render = False
        
        self.trainer = Trainer(
            env=self.env,
            agent=self.agent,
            buffer_capacity=self.buffer_capacity,
            batch_size=self.batch_size,
            update_frequency=self.update_frequency,
            log_frequency=self.log_frequency,
            save_dir=self.save_dir,
            render=self.render
        )
    
    def test_init(self):
        """测试训练器初始化"""
        # 检查训练器属性
        self.assertEqual(self.trainer.env, self.env)
        self.assertEqual(self.trainer.agent, self.agent)
        self.assertEqual(self.trainer.buffer_capacity, self.buffer_capacity)
        self.assertEqual(self.trainer.batch_size, self.batch_size)
        self.assertEqual(self.trainer.update_frequency, self.update_frequency)
        self.assertEqual(self.trainer.log_frequency, self.log_frequency)
        self.assertEqual(self.trainer.save_dir, self.save_dir)
        self.assertEqual(self.trainer.render, self.render)
        
        # 检查缓冲区
        self.assertIsNotNone(self.trainer.buffer)
        self.assertEqual(self.trainer.buffer.capacity, self.buffer_capacity)
    
    @patch('os.makedirs')
    @patch('time.time', return_value=1000)
    def test_train(self, mock_time, mock_makedirs):
        """测试训练方法"""
        # 训练
        num_episodes = 2
        max_steps_per_episode = 5
        results = self.trainer.train(
            num_episodes=num_episodes,
            max_steps_per_episode=max_steps_per_episode
        )
        
        # 检查环境和智能体方法是否被调用
        self.assertEqual(self.env.reset_called, num_episodes)
        self.assertEqual(self.env.step_called, num_episodes * max_steps_per_episode)
        self.assertEqual(self.agent.select_action_called, num_episodes * max_steps_per_episode)
        
        # 检查更新次数（每update_frequency步更新一次）
        expected_updates = (num_episodes * max_steps_per_episode) // self.update_frequency
        self.assertEqual(self.agent.update_called, expected_updates)
        
        # 检查结果
        self.assertIn('total_time', results)
        self.assertIn('episodes', results)
        self.assertIn('final_avg_reward', results)
        self.assertIn('final_avg_length', results)
        
        # 检查保存模型
        self.assertEqual(self.agent.save_called, 1)
    
    def test_evaluate(self):
        """测试评估方法"""
        # 评估
        num_episodes = 2
        results = self.trainer.evaluate(
            num_episodes=num_episodes,
            render=True
        )
        
        # 检查环境和智能体方法是否被调用
        self.assertEqual(self.env.reset_called, num_episodes)
        self.assertEqual(self.agent.select_action_called, num_episodes * 10)  # 每个episode 10步
        
        # 检查渲染
        self.assertEqual(self.env.render_called, num_episodes * 10)  # 每步都渲染
        
        # 检查结果
        self.assertIn('avg_reward', results)
        self.assertIn('avg_length', results)
        self.assertIn('total_reward', results)
        self.assertIn('episodes', results)
    
    def test_train_with_render(self):
        """测试带渲染的训练"""
        # 设置渲染
        self.trainer.render = True
        
        # 训练
        num_episodes = 1
        max_steps_per_episode = 5
        self.trainer.train(
            num_episodes=num_episodes,
            max_steps_per_episode=max_steps_per_episode
        )
        
        # 检查渲染
        self.assertEqual(self.env.render_called, num_episodes * max_steps_per_episode)
    
    def test_update_agent(self):
        """测试更新智能体"""
        # 添加一些样本到缓冲区
        for _ in range(self.batch_size * 2):
            self.trainer.buffer.add(
                state=np.zeros(10),
                action=0,
                reward=1.0,
                next_state=np.ones(10),
                done=False
            )
        
        # 更新智能体
        update_info = self.trainer._update_agent()
        
        # 检查智能体更新方法是否被调用
        self.assertEqual(self.agent.update_called, 1)
        
        # 检查更新信息
        self.assertIn('policy_loss', update_info)
        self.assertIn('value_loss', update_info)
        self.assertIn('entropy', update_info)

if __name__ == '__main__':
    unittest.main()