"""
找图找色相关接口封装
"""
import cv2
import numpy as np
from PIL import Image, ImageChops
from typing import List, Tuple, Union

class ImageSearch:

    def find_image(self, main_img: Image.Image, sub_img: Image.Image, threshold=0.8):
        """在大图main_img中寻找小图sub_img，返回(max_val, top_left, bottom_right)或None"""
        try:
            # 检查图像尺寸
            main_size = main_img.size
            sub_size = sub_img.size
            if sub_size[0] > main_size[0] or sub_size[1] > main_size[1]:
                print("小图尺寸大于大图，无法进行匹配")
                return 

            # 转换为OpenCV格式
            main = cv2.cvtColor(np.array(main_img), cv2.COLOR_RGB2BGR)
            sub = cv2.cvtColor(np.array(sub_img), cv2.COLOR_RGB2BGR)

            # 使用模板匹配
            res = cv2.matchTemplate(main, sub, cv2.TM_CCOEFF_NORMED)
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(res)

            # 如果匹配度小于阈值，返回None
            if max_val <= threshold:
                return

            # 获取匹配区域
            h, w = sub.shape[:2]
            top_left = max_loc

            # 返回匹配区域
            bottom_right = (top_left[0] + w, top_left[1] + h)
            return (max_val, top_left, bottom_right)
        except Exception as e:
            print(f"图片匹配失败: {str(e)}")

    def find_image_similarity(self, img1: Image.Image, img2: Image.Image):
        """计算两图结构相似度[0-1]"""
        # 确保图像尺寸一致
        if img1.size != img2.size:
            raise ValueError("Images must have same size")
        # 计算差异图
        diff = ImageChops.difference(img1.convert('RGB'), img2.convert('RGB'))
        # 计算总差异值（每个像素的RGB三通道差异之和）
        sum_diff = sum(r + g + b for (r, g, b) in diff.getdata())
        # 计算最大可能差异值（255 * 3 channels）
        max_diff = 255 * 3 * diff.size[0] * diff.size[1]
        # 计算结构相似度
        return 1 - (sum_diff / max_diff)

    def _find_color(self, image: Image.Image, region: tuple, rgb_color: Union[str, Tuple[int, int, int]], similarity: float) -> List[Tuple[float, Tuple[int, int]]]:
        '''
        找色
        :param image: 图片
        :param region: 查找区域(x1, y1, x2, y2)
        :param rgb_color: 十六进制颜色或RGB元组
        :param similarity: 相似度
        :return: (最高相似度, 最佳匹配点坐标)
        '''
        # img = np.array(image)
        # res = np.absolute(img - self.hex2rgb(rgb_color))
        # # 计算每个像素的相似度
        # print(f"res: {res.shape}")
        # sim = int((1 - similarity) * 255)
        # print(f"相似度阈值: {sim}")
        # res = np.argwhere(np.all(res <= sim, axis=2))
        # print(f"匹配点数量: {len(res)}")
        # res = res + (region[1], region[0])  # 添加region偏移
        # return res[:, [1, 0]]

        # 转换为numpy数组
        img = np.array(image)
        target_color = self.hex2rgb(rgb_color)
        
        # 计算每个像素与目标颜色的差异
        color_diff = np.absolute(img - target_color)
        
        # 计算每个像素的相似度
        max_diff = 255.0  # RGB每个通道的最大差异值
        pixel_similarities = 1.0 - np.mean(color_diff / max_diff, axis=2)  # 归一化并取平均
        # print(f"pixel_similarities: {pixel_similarities.shape}, max: {np.max(pixel_similarities)}, min: {np.min(pixel_similarities)}")
        
        # 找到满足相似度阈值的点
        matches = np.argwhere(pixel_similarities >= similarity)
        # print(f"匹配点数量: {len(matches)}")
        if len(matches) == 0:
            return []
            
        # 转换坐标并计算最终相似度
        points = []
        for y, x in matches:
            # 计算实际坐标（考虑region偏移）
            real_x = x + region[0]
            real_y = y + region[1]
            # 获取该点的相似度
            sim = float(pixel_similarities[y, x])
            points.append((sim, (real_x, real_y)))
            
        # 如果没有找到匹配点
        if not points:
            return []
        
        return points
    
    def find_color(self, image: Image.Image, region: tuple, rgb_color: Union[str, Tuple[int, int, int]], similarity: float) -> Tuple[float, Tuple[int, int]]:
        '''
        找色
        :param image: 图片
        :param region: 查找区域(x1, y1, x2, y2)
        :param rgb_color: 十六进制颜色或RGB元组，支持单点找色和多点找色，单点找色时为十六进制颜色或RGB元组，多点找色时为'RRGGBB|RRGGBB|...'
        :param similarity: 相似度
        :return: (相似度, 坐标)
        '''
        colors = self.parse_color(rgb_color)
        if len(colors) < 2:
            points = self._find_color(image, region, colors[0], similarity)

            # 返回相似度最高的点
            if not points:
                return 0, None
            return max(points, key=lambda x: x[0])
        
        points = []
        for color in colors:
            sim, pos = self._find_color(image, region, color, similarity)
            if sim >= similarity:  # 只添加匹配度达标的点
                points.append((sim, pos))
        if not points:  # 如果没有找到任何匹配的点
            return 0, None
        return max(points, key=lambda x: x[0])
        
    def find_color_numbers(self, image: Image.Image, region: tuple, rgb_colors: Union[str, Tuple[int, int, int]], similarity: float) -> int:
        '''
        多点找色，返回颜色数量
        :param image: 图片
        :param region: 查找区域(x1, y1, x2, y2)
        :param rgb_colors: 支持单点找色和多点找色，单点找色时为十六进制颜色或RGB元组，多点找色时为'RRGGBB|RRGGBB|...'
        :param similarity: 相似度
        :return: 颜色数量
        '''
        return len(self._find_color(image, region, rgb_colors, similarity))
    
    def compare_color(self, image: Image.Image, rgb_color: Union[str, Tuple[int, int, int]], similarity: float) -> bool:
        '''
        比较颜色
        :param image: 图片
        :param rgb_color: 十六进制颜色或RGB元组
        :param similarity: 相似度
        :return: 是否相似
        '''
        img = np.array(image)
        target_color = self.hex2rgb(rgb_color)
        color = img[1][1]
        res = np.absolute(color - target_color)
        similarity = int((1 - similarity) * 255)
        return np.amax(res) <= similarity

    def parse_color(self, color_rgb: Union[str, Tuple[int, int, int]]) -> List[np.ndarray]:
        """
        解析颜色值，支持多种格式：
        - RGB元组：(r,g,b)
        - 单点找色：'#RRGGBB'
        - 多点找色：'RRGGBB|RRGGBB|...'
        返回：单个颜色时返回[np.ndarray]，多个颜色时返回[np.ndarray,...]列表
        """
        if isinstance(color_rgb, tuple):
            return [self.hex2rgb(color_rgb)]
        elif isinstance(color_rgb, str):
            color_rgb = color_rgb.replace('#', '')
            if '|' not in color_rgb:
                return [self.hex2rgb(color_rgb)]
            return [self.hex2rgb(color) for color in color_rgb.split('|')]
        else:
            raise ValueError(f"Invalid color format: {color_rgb}")

    def hex2rgb(self, hex_color: Union[str, Tuple[int, int, int], np.ndarray]) -> np.ndarray:
        '''
        十六进制颜色转RGB
        :param hex_color: 十六进制颜色或RGB元组
        :return: RGB颜色
        '''
        if isinstance(hex_color, np.ndarray):
            return hex_color
        elif isinstance(hex_color, tuple):
            return np.array(hex_color)
        elif isinstance(hex_color, str):
            if '|' in hex_color:
                raise ValueError(f"Unsupported type: {hex_color}")
            if len(hex_color) != 6:
                raise ValueError(f"Invalid hex color: {hex_color}")
            return np.array(tuple(int(hex_color[i:i + 2], 16) for i in (0, 2, 4)))
        else:
            raise ValueError(f"Invalid hex color: {hex_color}")

    def process_char_region(self, char_region: Image.Image) -> Image.Image:
        """
        处理字符区域图像，包括二值化、轮廓提取等预处理步骤
        :param char_region: PIL格式的字符区域图像
        :return: 处理后的PIL格式图像，如果处理失败返回原图
        """
        try:
            # 转换为OpenCV格式
            char_cv = cv2.cvtColor(np.array(char_region), cv2.COLOR_RGB2BGR)
            
            # 转换为灰度图
            gray = cv2.cvtColor(char_cv, cv2.COLOR_BGR2GRAY)
            
            # 二值化
            _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            
            # 查找轮廓
            contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # 如果找到轮廓，创建掩码
            if contours:
                # 创建掩码
                mask = np.zeros_like(binary)
                # 填充最大的轮廓（通常是字符本身）
                max_contour = max(contours, key=cv2.contourArea)
                cv2.drawContours(mask, [max_contour], -1, 255, -1)
                
                # 应用掩码到原图
                char_cv_masked = cv2.bitwise_and(char_cv, char_cv, mask=mask)
                
                # 转回PIL格式
                return Image.fromarray(cv2.cvtColor(char_cv_masked, cv2.COLOR_BGR2RGB))
            
            # 如果没有找到轮廓，返回原图
            return char_region
            
        except Exception as e:
            print(f"字符区域处理失败: {str(e)}")
            return char_region
        