"""
统一对外API入口
"""
import time
from .window_ops import WindowOps
from .screenshot import Screenshot
from .detection import Detection
from .image_search import ImageSearch
from .ocr import OCR
from PIL import Image
import numpy as np
import os
from PIL import ImageDraw
import cv2
from sklearn.cluster import KMeans
from typing import List

class AssistBasicToolkit:
    def __init__(self):
        self.window_ops = WindowOps()

        self.hwnd: int = 0
        self.screenshot: Screenshot = None
        self.detection: Detection = None
        self.image_search: ImageSearch = None
        self.ocr: OCR = None
    
    def register_and_bind_window_objects(self, win_title):
        hwnd = self.window_ops.find_window_by_partial_title(win_title)
        if not hwnd: raise ValueError(f"window {win_title} not found")

        self.window_ops.set_window_foreground(hwnd)

        self.hwnd = hwnd
        self.screenshot = Screenshot(hwnd)
        self.detection = Detection()
        self.image_search = ImageSearch()
        self.ocr = OCR()
        return hwnd

    def verify_objects(self):
        if not self.hwnd:
            raise ValueError("object hwnd is not registered")
        if not self.screenshot:
            raise ValueError("object screenshot is not registered")
        if not self.detection:
            raise ValueError("object detection is not registered")
        if not self.image_search:
            raise ValueError("object image_search is not registered")
        if not self.ocr:
            raise ValueError("object ocr is not registered")
        return True
    
    def capture_region(self, region=None):
        """
        截图指定区域或截取客户区，返回PIL.Image对象。
        """
        if not self.screenshot:
            return
        if not region:
            return self.screenshot.capture_client_area()
        return self.screenshot.capture_region(region)
    
    def find_image(self, sub_img, region):
        """
        在指定区域截图后，查找小图sub_img，返回(max_val, 匹配区域左上角, 匹配区域右下角)，坐标为屏幕坐标。
        region: (left, top, right, bottom)
        :return: (相似度, (x1, y1), (x2, y2))
        """
        region_img = self.capture_region(region)
        if region_img is None:
            return
        result = self.image_search.find_image(region_img, sub_img)
        if not result:
            return
        max_val, (x1, y1), (x2, y2) = result
        x1, y1, x2, y2 = map(int, [x1, y1, x2, y2])

        # 边界保护
        x1, y1 = max(0, x1), max(0, y1)
        x2, y2 = min(region_img.size[0], x2), min(region_img.size[1], y2)
        if x2 <= x1 or y2 <= y1:
            return

        # 转为客户区坐标
        left, top, _, _ = region
        cx1, cy1 = x1 + left, y1 + top
        cx2, cy2 = x2 + left, y2 + top
        return max_val, (cx1, cy1), (cx2, cy2)

    def find_color(self, target_color, region, similarity=0.8):
        """
        在指定区域截图后，查找颜色target_color，返回(x, y)，坐标为屏幕坐标。
        region: (left, top, right, bottom)
        :return: (相似度, (x, y))
        """
        region_img = self.capture_region(region)
        if region_img is None:
            return 
        sim, pos = self.image_search.find_color(region_img, region, target_color, similarity)
        if not pos:
            return

        x, y = pos

        # 边界保护
        x, y = map(int, [x, y])
        x, y = max(0, x), max(0, y)
        if x >= region_img.size[0] or y >= region_img.size[1]:
            return

        # 转为客户区坐标
        left, top, _, _ = region
        cx, cy = x + left, y + top
        return sim, (cx, cy)
    
    def created_draw(self, image: Image.Image):
        debug_img = image.copy()
        draw = ImageDraw.Draw(debug_img)
        return draw, debug_img
    
    def output_draw(self, image: Image.Image, name='', temp_dir=None):
        # 创建临时目录用于调试
        temp_dir = temp_dir or os.path.join(os.path.dirname(os.path.dirname(__file__)), "temp")
        os.makedirs(temp_dir, exist_ok=True)
        image.save(os.path.join(temp_dir, f"{name}_{int(time.time() * 1000)}.png"))

    def find_ocr(self, region=None):
        '''
        ocr识别指定区域，返回识别结果。
        '''
        region_img = self.capture_region(region)
        if region_img is None:
            return []
        return self.ocr.recognize(region_img)
    
    def find_ocr_text(self, region=None):
        """
        ocr识别指定区域，返回识别到的文本列表。
        """
        region_img = self.capture_region(region)
        if region_img is None:
            return []
        return self.ocr.recognize_text(region_img)
    
    def find_ocr_single_line(self, region=None):
        """
        ocr识别指定区域，返回识别到的单行文本字符串。
        """
        region_img = self.capture_region(region)
        if region_img is None:
            return ''
        return self.ocr.recognize_single_line(region_img)

    def find_ocr_position(self, target_text, region, fuzzy=False, use_regex=False, estimate_inner_bbox=True):
        """
        区域找字，返回格式为[(text, (left, top), (right, bottom))]，坐标为屏幕坐标。
        """
        region_img = self.capture_region(region)
        if region_img is None:
            return []
        results = self.ocr.find_text_in_image(
            region_img, 
            target_text, 
            fuzzy=fuzzy, 
            use_regex=use_regex, 
            estimate_inner_bbox=estimate_inner_bbox
        )

        draw, debug_img = self.created_draw(region_img)

        left, top, _, _ = region
        output = []
        for item in results:
            text = item.get('text')
            x1, y1, x2, y2 = item.get('bbox', (0, 0, 0, 0))
            x1, y1, x2, y2 = map(int, [x1, y1, x2, y2])

            # 边界保护
            x1, y1 = max(0, x1), max(0, y1)
            x2, y2 = min(region_img.size[0], x2), min(region_img.size[1], y2)
            if x2 <= x1 or y2 <= y1:
                continue

            # 标记识别到的文字区域线框为红色
            draw.rectangle([x1, y1, x2, y2], outline="red", width=2)

            # 转为客户区坐标
            cx1, cy1 = x1 + left, y1 + top
            cx2, cy2 = x2 + left, y2 + top
            output.append((text, (cx1, cy1), (cx2, cy2)))
        
        # 保存调试图片
        self.output_draw(debug_img, name='debug_ocr_position')

        return sorted(output, key=lambda x: x[1][0])  # 按坐标x轴排序

    def find_ocr_by_color(self, target_text, region, color_rgb, similarity=0.8, fuzzy=False, use_regex=False, estimate_inner_bbox=True):
        """
        区域找字并按颜色精准筛选，返回('颜色相似度', (left, top), (right, bottom))，坐标为屏幕坐标。
        :param color_rgb: 目标颜色，支持以下格式：
            - RGB元组：(r,g,b)
            - 单点找色：'#RRGGBB'或'RRGGBB'
            - 多点找色：'RRGGBB|RRGGBB|...'（支持渐变色）
        :param similarity: 颜色相似度，范围0-1，默认0.8。设置建议：
            - 严格匹配：0.9以上
            - 一般匹配：0.7-0.9
            - 宽松匹配：0.5-0.7
        """
        # 获取区域截图
        region_img = self.capture_region(region)
        if region_img is None:
            return 0, None, None, ''
                        
        # 获取OCR结果
        results = self.ocr.find_text_in_image(
            region_img, 
            target_text, 
            fuzzy=fuzzy, 
            use_regex=use_regex, 
            estimate_inner_bbox=estimate_inner_bbox
        )
        
        bests = []
        
        draw, debug_img = self.created_draw(region_img)
        
        left, top, _, _ = region

        # 先用蓝色标记所有text_region
        for item in results:
            x1, y1, x2, y2 = item.get('bbox', (0, 0, 0, 0))
            x1, y1, x2, y2 = map(int, [x1, y1, x2, y2])
            text = item.get('text', '')
            
            # 边界保护
            x1, y1 = max(0, x1), max(0, y1)
            x2, y2 = min(region_img.size[0], x2), min(region_img.size[1], y2)
            if x2 <= x1 or y2 <= y1:
                continue
                
            # 蓝色标记所有识别到的文字区域
            draw.rectangle([x1, y1, x2, y2], outline="blue", width=2)
            
            # 提取文字区域图像
            text_region = region_img.crop((x1, y1, x2, y2))
            
            # 检查颜色匹配
            sim, pos = self.image_search.find_color(
                text_region, 
                (0, 0, x2-x1, y2-y1),  # 相对于裁剪区域的坐标
                color_rgb,
                similarity
            )

            if not pos:  # 没有匹配到颜色
                continue
            if sim < similarity:  # 匹配度不达标
                continue
                
            # 红色标记颜色匹配成功的区域
            draw.rectangle([x1, y1, x2, y2], outline="red", width=2)
            
            # 转为客户区坐标
            cx1, cy1 = x1 + left, y1 + top
            cx2, cy2 = x2 + left, y2 + top
            bests.append((sim, (cx1, cy1), (cx2, cy2), text))
                
        # 保存调试图片
        self.output_draw(debug_img, name='debug_color_ocr')

        if not bests:
            return 0, None, None, ''
        return max(bests, key=lambda x: x[0])

    def find_detect_targets(self, target_name, region, conf_thres=0.3):
        """
        基于yolo模型在区域检测目标，传入目标名称和检测区域，返回(相似度, (left, top), (right, bottom))，坐标为屏幕坐标。
        :param target_name: 目标类别名（如'npc'）
        :param region: (left, top, right, bottom)
        :param conf_thres: 置信度阈值
        :return: (conf, (sx1, sy1), (sx2, sy2))
        """
        region_img = self.capture_region(region)
        if region_img is None:
            return

        # 目标检测，返回单个Results对象
        results = self.detection.detect_objects(region_img)

        found = []
        for box in results.boxes:
            cls_id = int(box.cls.item())
            conf = float(box.conf.item())

            # 获取类别名
            name = results.names[cls_id] if hasattr(results, 'names') else str(cls_id)
            if name != target_name:
                continue
            if conf < conf_thres:
                continue
            
            x1, y1, x2, y2 = map(int, box.xyxy[0].cpu().numpy())

            # 边界保护
            x1, y1 = max(0, x1), max(0, y1)
            x2, y2 = min(region_img.size[0], x2), min(region_img.size[1], y2)
            if x2 <= x1 or y2 <= y1:
                continue

            # 转为客户区坐标
            left, top, _, _ = region
            cx1, cy1 = x1 + left, y1 + top
            cx2, cy2 = x2 + left, y2 + top
            found.append((conf, (cx1, cy1), (cx2, cy2), name))

        if not found:
            return

        # 返回置信度最高的目标
        return max(found, key=lambda x: x[0])

    def merge_char_regions(self, matched_chars: List[dict]) -> List[dict]:
        """
        合并相邻的字符区域，并拼接文本
        :param matched_chars: 匹配到的字符区域列表，每项包含sim（相似度）、bbox（边界框）、char（字符）、y（纵坐标）
        :return: 合并后的区域列表，每项包含sim、bbox、text
        """
        if not matched_chars:
            return []
            
        # 按y坐标分组（相同行的字符）
        matched_chars.sort(key=lambda x: x['y'])
        line_groups = []
        current_line = []

        for char in matched_chars:
            if not current_line:
                current_line.append(char)
            else:
                # 如果y坐标相差不大，认为是同一行
                if abs(char['y'] - current_line[0]['y']) <= 5:
                    current_line.append(char)
                else:
                    line_groups.append(current_line)
                    current_line = [char]
        if current_line:
            line_groups.append(current_line)
        
        merged_regions = []
        for line in line_groups:
            # 按x坐标排序
            line.sort(key=lambda x: x['bbox'][0])
            current_region = {
                'sim': line[0]['sim'],
                'bbox': line[0]['bbox'],
                'text': line[0]['char']
            }
            for i in range(1, len(line)):
                current_bbox = current_region['bbox']
                next_bbox = line[i]['bbox']
                # 如果两个字符区域相邻或重叠
                if next_bbox[0] - current_bbox[2] <= 5:
                    # 合并区域
                    current_region = {
                        'sim': min(current_region['sim'], line[i]['sim']),
                        'bbox': (
                            current_bbox[0],
                            min(current_bbox[1], next_bbox[1]),
                            next_bbox[2],
                            max(current_bbox[3], next_bbox[3])
                        ),
                        'text': current_region['text'] + line[i]['char']
                    }
                else:
                    merged_regions.append(current_region)
                    current_region = {
                        'sim': line[i]['sim'],
                        'bbox': line[i]['bbox'],
                        'text': line[i]['char']
                    }
            merged_regions.append(current_region)
        return merged_regions

    def find_color_text_regions(self, region, color_rgb, similarity=0.8):
        """
        查找区域内目标颜色的文字区域，返回格式为[(相似度, (left, top), (right, bottom), 框选文本)]，坐标为屏幕坐标。
        会自动合并相邻的同色文字区域。
        :param region: (left, top, right, bottom)
        :param color_rgb: 目标颜色
        :param similarity: 颜色相似度
        :return: [(相似度, (left, top), (right, bottom), 框选文本)]
        """
        # 获取区域截图
        region_img = self.capture_region(region)
        if region_img is None:
            return []
                    
        # 获取每个字的位置信息
        char_positions = self.ocr.find_char_positions(region_img)
        
        draw, debug_img = self.created_draw(region_img)
        
        # 存储匹配的字符区域
        matched_chars = []
        
        # 检查每个字符的颜色
        for item in char_positions:
            x1, y1, x2, y2 = item.get('bbox', (0, 0, 0, 0))
            x1, y1, x2, y2 = map(int, [x1, y1, x2, y2])
            char = item.get('char', '')
            if not char:
                continue
            
            # 边界保护
            x1, y1 = max(0, x1), max(0, y1)
            x2, y2 = min(region_img.size[0], x2), min(region_img.size[1], y2)
            if x2 <= x1 or y2 <= y1:
                continue
                
            # 提取字符区域图像
            char_region = region_img.crop((x1, y1, x2, y2))
            
            # 处理字符区域（使用新的process_char_region方法）
            processed_region = self.image_search.process_char_region(char_region)
            
            # 检查颜色匹配
            sim, pos = self.image_search.find_color(
                processed_region, 
                (0, 0, x2-x1, y2-y1),
                color_rgb,
                similarity
            )

            if not pos:  # 没有匹配到颜色
                continue
            if sim < similarity:  # 匹配度不达标
                continue
            
            matched_chars.append({
                'char': char,
                'sim': sim,
                'bbox': (x1, y1, x2, y2),
                'y': y1  # 用于后续行分组
            })
        
        # 如果没有找到任何匹配的字符
        if not matched_chars:
            self.output_draw(debug_img, name='debug_color_regions')
            return []
        
        # 合并相邻的字符区域
        merged_regions = self.merge_char_regions(matched_chars)
        
        left, top, _, _ = region

        # 转换为屏幕坐标并生成结果
        result = []
        for region in merged_regions:
            x1, y1, x2, y2 = region['bbox']
            # 红色标记合并后的区域
            draw.rectangle([x1, y1, x2, y2], outline="red", width=2)
            
            # 转为客户区坐标
            cx1, cy1 = x1 + left, y1 + top
            cx2, cy2 = x2 + left, y2 + top
            result.append((region['sim'], (cx1, cy1), (cx2, cy2), region['text']))
        
        # 保存调试图片
        self.output_draw(debug_img, name='debug_color_regions')
        
        return sorted(result, key=lambda x: x[0], reverse=True)

    def close(self):
        objects = ['screenshot', 'detection', 'image_search', 'ocr']
        for name in objects:
            if not hasattr(self, name):
                continue
            obj = getattr(self, name)
            if not hasattr(obj, 'close'):
                continue
            close = getattr(obj, 'close')
            close()
