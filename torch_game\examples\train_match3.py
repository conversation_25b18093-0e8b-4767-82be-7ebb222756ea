"""
训练智能体玩Match-3游戏的示例脚本
"""

import os
import argparse
import numpy as np
import matplotlib.pyplot as plt

from torch_game.env import Match3Env
from torch_game.agents import PPOAgent
from torch_game.utils import Trainer

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='Train an agent to play Match-3 game')
    
    # 环境参数
    parser.add_argument('--board_size', type=int, default=6, help='Size of the game board')
    parser.add_argument('--num_colors', type=int, default=5, help='Number of colors in the game')
    
    # 智能体参数
    parser.add_argument('--hidden_dims', type=int, nargs='+', default=[256, 128], help='Hidden dimensions of the networks')
    parser.add_argument('--lr', type=float, default=3e-4, help='Learning rate')
    parser.add_argument('--gamma', type=float, default=0.99, help='Discount factor')
    parser.add_argument('--gae_lambda', type=float, default=0.95, help='GAE lambda parameter')
    parser.add_argument('--clip_epsilon', type=float, default=0.2, help='PPO clip epsilon')
    
    # 训练参数
    parser.add_argument('--num_episodes', type=int, default=1000, help='Number of training episodes')
    parser.add_argument('--max_steps', type=int, default=100, help='Maximum steps per episode')
    parser.add_argument('--buffer_capacity', type=int, default=10000, help='Capacity of replay buffer')
    parser.add_argument('--batch_size', type=int, default=64, help='Batch size for training')
    parser.add_argument('--update_frequency', type=int, default=4, help='Frequency of agent updates')
    parser.add_argument('--log_frequency', type=int, default=10, help='Frequency of logging')
    
    # 评估参数
    parser.add_argument('--eval_episodes', type=int, default=10, help='Number of evaluation episodes')
    parser.add_argument('--render', action='store_true', help='Render the environment during evaluation')
    
    # 保存/加载参数
    parser.add_argument('--save_dir', type=str, default='checkpoints', help='Directory to save checkpoints')
    parser.add_argument('--load_model', type=str, default=None, help='Path to load a pre-trained model')
    parser.add_argument('--eval_only', action='store_true', help='Only run evaluation, no training')
    
    # 可视化参数
    parser.add_argument('--plot_results', action='store_true', help='Plot training results')
    
    return parser.parse_args()

def plot_training_results(results, save_path=None):
    """绘制训练结果"""
    # 提取数据
    episodes = results['episodes']
    rewards = [ep['reward'] for ep in episodes]
    lengths = [ep['length'] for ep in episodes]
    
    # 创建图形
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(10, 8), sharex=True)
    
    # 绘制奖励
    ax1.plot(rewards, 'b-')
    ax1.set_ylabel('Reward')
    ax1.set_title('Training Results')
    ax1.grid(True)
    
    # 绘制回合长度
    ax2.plot(lengths, 'r-')
    ax2.set_xlabel('Episode')
    ax2.set_ylabel('Length')
    ax2.grid(True)
    
    plt.tight_layout()
    
    # 保存图形
    if save_path:
        plt.savefig(save_path)
    
    plt.show()

def main():
    """主函数"""
    # 解析参数
    args = parse_args()
    
    # 创建保存目录
    os.makedirs(args.save_dir, exist_ok=True)
    
    # 创建环境
    env = Match3Env(
        board_size=args.board_size,
        num_colors=args.num_colors
    )
    
    # 计算状态和动作维度
    state_dim = env.observation_space.shape[0]
    action_dim = env.action_space.n
    
    # 创建智能体
    agent = PPOAgent(
        state_dim=state_dim,
        action_dim=action_dim,
        hidden_dims=args.hidden_dims,
        lr=args.lr,
        gamma=args.gamma,
        gae_lambda=args.gae_lambda,
        clip_epsilon=args.clip_epsilon
    )
    
    # 加载预训练模型（如果指定）
    if args.load_model:
        print(f"Loading model from {args.load_model}")
        agent.load(args.load_model)
    
    # 创建训练器
    trainer = Trainer(
        env=env,
        agent=agent,
        buffer_capacity=args.buffer_capacity,
        batch_size=args.batch_size,
        update_frequency=args.update_frequency,
        log_frequency=args.log_frequency,
        save_dir=args.save_dir,
        render=args.render
    )
    
    # 训练或评估
    if not args.eval_only:
        # 训练智能体
        print("Training agent...")
        results = trainer.train(
            num_episodes=args.num_episodes,
            max_steps_per_episode=args.max_steps
        )
        
        # 绘制训练结果
        if args.plot_results:
            plot_training_results(results, save_path=os.path.join(args.save_dir, 'training_results.png'))
    
    # 评估智能体
    print("Evaluating agent...")
    eval_results = trainer.evaluate(
        num_episodes=args.eval_episodes,
        render=args.render
    )
    
    # 打印评估结果
    print(f"Evaluation results:")
    print(f"  Average reward: {eval_results['avg_reward']:.2f}")
    print(f"  Average episode length: {eval_results['avg_length']:.2f}")
    print(f"  Total reward: {eval_results['total_reward']:.2f}")

if __name__ == "__main__":
    main()