import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Tuple

class PolicyNetwork(nn.Module):
    """
    策略网络，用于生成动作概率分布。
    使用一个多层感知机来处理状态输入，输出动作概率。
    """
    
    def __init__(self, state_dim: int, action_dim: int, hidden_dims: Tuple[int, ...] = (256, 128)):
        """
        初始化策略网络。
        
        Args:
            state_dim: 状态空间维度
            action_dim: 动作空间维度
            hidden_dims: 隐藏层维度的元组，默认为(256, 128)
        """
        super().__init__()
        
        # 构建网络层
        layers = []
        prev_dim = state_dim
        
        # 添加隐藏层
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.ReLU(),
                nn.LayerNorm(hidden_dim),  # 添加层归一化以提高训练稳定性
            ])
            prev_dim = hidden_dim
        
        # 输出层，不带激活函数
        layers.append(nn.Linear(prev_dim, action_dim))
        
        self.network = nn.Sequential(*layers)
    
    def forward(self, state: torch.Tensor) -> torch.Tensor:
        """
        前向传播，计算动作概率分布。
        
        Args:
            state: 状态输入张量，形状为(batch_size, state_dim)
            
        Returns:
            torch.Tensor: 动作概率分布，形状为(batch_size, action_dim)
        """
        # 通过网络计算logits
        logits = self.network(state)
        
        # 使用softmax转换为概率分布
        probs = F.softmax(logits, dim=-1)
        
        return probs
    
    def get_action(self, state: torch.Tensor, deterministic: bool = False) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        根据状态选择动作。
        
        Args:
            state: 状态输入张量
            deterministic: 是否使用确定性策略（选择概率最高的动作）
            
        Returns:
            Tuple[torch.Tensor, torch.Tensor]: (选择的动作, 动作概率)
        """
        # 获取动作概率分布
        probs = self.forward(state)
        
        if deterministic:
            # 选择概率最高的动作
            action = torch.argmax(probs, dim=-1)
        else:
            # 根据概率分布采样动作
            dist = torch.distributions.Categorical(probs)
            action = dist.sample()
        
        # 获取选中动作的概率
        action_prob = probs.gather(1, action.unsqueeze(-1)).squeeze(-1)
        
        return action, action_prob
    
    def evaluate_actions(self, state: torch.Tensor, action: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        评估给定的状态-动作对。
        
        Args:
            state: 状态输入张量
            action: 动作输入张量
            
        Returns:
            Tuple[torch.Tensor, torch.Tensor, torch.Tensor]: 
                - 动作对数概率
                - 动作概率熵
                - 动作概率
        """
        # 获取动作概率分布
        probs = self.forward(state)
        
        # 创建分布对象
        dist = torch.distributions.Categorical(probs)
        
        # 计算动作的对数概率
        log_prob = dist.log_prob(action)
        
        # 计算分布的熵
        entropy = dist.entropy()
        
        # 获取选中动作的概率
        action_prob = probs.gather(1, action.unsqueeze(-1)).squeeze(-1)
        
        return log_prob, entropy, action_prob