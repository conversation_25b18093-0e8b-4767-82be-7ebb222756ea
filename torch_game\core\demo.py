"""
演示脚本
展示如何使用框架训练和评估智能体
"""

import os
import torch
import numpy as np
import time
import argparse

from torch_game.env import Match3Env
from torch_game.agents import PPOAgent
from torch_game.utils import Config, Trainer

def demo_match3():
    """Match-3游戏演示"""
    print("创建Match-3游戏环境...")
    env = Match3Env(board_size=(6, 6), n_colors=5, max_steps=50)
    
    # 显示环境信息
    print(f"观察空间: {env.observation_space}")
    print(f"动作空间: {env.action_space}")
    
    # 重置环境
    state = env.reset()
    print(f"初始状态形状: {state.shape}")
    
    # 随机玩几步
    print("\n随机玩几步...")
    for i in range(5):
        action = env.action_space.sample()
        next_state, reward, done, info = env.step(action)
        
        print(f"步骤 {i+1}:")
        print(f"  动作: {action}")
        print(f"  奖励: {reward}")
        print(f"  游戏结束: {done}")
        print(f"  信息: {info}")
        
        if done:
            break
    
    # 关闭环境
    env.close()

def demo_training():
    """训练演示"""
    print("创建环境和智能体...")
    
    # 创建环境
    env = Match3Env(board_size=(6, 6), n_colors=5, max_steps=50)
    
    # 创建智能体
    state_dim = np.prod(env.observation_space.shape)
    action_dim = env.action_space.n
    
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"使用设备: {device}")
    
    agent = PPOAgent(
        state_dim=state_dim,
        action_dim=action_dim,
        hidden_dim=128,
        device=device
    )
    
    # 创建配置
    config = Config({
        'env': {
            'board_size': [6, 6],
            'n_colors': 5,
            'max_steps': 50
        },
        'agent': {
            'type': 'ppo',
            'hidden_dim': 128,
            'lr': 3e-4
        },
        'training': {
            'n_episodes': 10,  # 演示用，实际训练需要更多回合
            'batch_size': 32,
            'update_interval': 128,
            'n_epochs': 3,
            'eval_interval': 5,
            'save_interval': 5,
            'log_interval': 1
        },
        'logging': {
            'log_dir': 'demo_logs',
            'model_dir': 'demo_models'
        }
    })
    
    # 创建训练器
    trainer = Trainer(agent, env, config)
    
    # 开始训练
    print("\n开始训练（演示模式，仅训练10回合）...")
    trainer.train()
    
    # 保存模型
    model_path = os.path.join('demo_models', 'demo_model.pth')
    trainer.save_model('demo_model.pth')
    print(f"模型已保存到: {model_path}")
    
    # 关闭环境
    env.close()
    
    return model_path

def demo_evaluation(model_path):
    """评估演示"""
    print("\n评估训练好的智能体...")
    
    # 创建环境
    env = Match3Env(board_size=(6, 6), n_colors=5, max_steps=50)
    
    # 创建智能体
    state_dim = np.prod(env.observation_space.shape)
    action_dim = env.action_space.n
    
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    
    agent = PPOAgent(
        state_dim=state_dim,
        action_dim=action_dim,
        hidden_dim=128,
        device=device
    )
    
    # 加载模型
    agent.load(model_path)
    agent.eval()
    
    # 评估
    n_episodes = 3
    total_rewards = []
    
    for episode in range(n_episodes):
        state = env.reset()
        done = False
        episode_reward = 0
        
        print(f"\n回合 {episode+1}/{n_episodes}")
        
        while not done:
            # 渲染环境
            env.render()
            time.sleep(0.5)  # 延迟，便于观察
            
            # 选择动作
            action = agent.select_action(state.reshape(-1), deterministic=True)
            
            # 执行动作
            next_state, reward, done, info = env.step(action)
            episode_reward += reward
            
            print(f"  动作: {action}, 奖励: {reward:.2f}")
            
            # 更新状态
            state = next_state
            
        total_rewards.append(episode_reward)
        print(f"  回合奖励: {episode_reward:.2f}")
    
    # 打印统计信息
    print("\n评估结果:")
    print(f"平均奖励: {np.mean(total_rewards):.2f}")
    
    # 关闭环境
    env.close()

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='演示脚本')
    parser.add_argument('--mode', type=str, default='all', 
                        choices=['env', 'train', 'eval', 'all'],
                        help='演示模式')
    parser.add_argument('--model', type=str, default=None,
                        help='评估模式下的模型路径')
    args = parser.parse_args()
    
    if args.mode == 'env' or args.mode == 'all':
        print("=" * 50)
        print("环境演示")
        print("=" * 50)
        demo_match3()
    
    if args.mode == 'train' or args.mode == 'all':
        print("\n" + "=" * 50)
        print("训练演示")
        print("=" * 50)
        model_path = demo_training()
    else:
        model_path = args.model
    
    if args.mode == 'eval' or args.mode == 'all':
        if model_path is None:
            print("\n错误: 评估模式需要指定模型路径")
            return
            
        print("\n" + "=" * 50)
        print("评估演示")
        print("=" * 50)
        demo_evaluation(model_path)

if __name__ == '__main__':
    main()