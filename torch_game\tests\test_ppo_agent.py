import unittest
import torch
import numpy as np

from torch_game.agents.ppo_agent import PPOAgent

class TestPPOAgent(unittest.TestCase):
    """测试PPOAgent类"""
    
    def setUp(self):
        """在每个测试方法之前设置智能体"""
        self.state_dim = 10
        self.action_dim = 4
        self.hidden_dims = (64, 32)
        self.lr = 3e-4
        self.gamma = 0.99
        self.gae_lambda = 0.95
        self.clip_epsilon = 0.2
        
        self.agent = PPOAgent(
            state_dim=self.state_dim,
            action_dim=self.action_dim,
            hidden_dims=self.hidden_dims,
            lr=self.lr,
            gamma=self.gamma,
            gae_lambda=self.gae_lambda,
            clip_epsilon=self.clip_epsilon
        )
        
        # 创建一些示例数据
        self.state = np.random.rand(self.state_dim).astype(np.float32)
        self.states = np.random.rand(32, self.state_dim).astype(np.float32)
        self.actions = np.random.randint(0, self.action_dim, size=32)
        self.rewards = np.random.rand(32).astype(np.float32)
        self.next_states = np.random.rand(32, self.state_dim).astype(np.float32)
        self.dones = np.random.randint(0, 2, size=32).astype(bool)
    
    def test_init(self):
        """测试智能体初始化"""
        # 检查智能体属性
        self.assertEqual(self.agent.state_dim, self.state_dim)
        self.assertEqual(self.agent.action_dim, self.action_dim)
        self.assertEqual(self.agent.gamma, self.gamma)
        self.assertEqual(self.agent.gae_lambda, self.gae_lambda)
        self.assertEqual(self.agent.clip_epsilon, self.clip_epsilon)
        
        # 检查网络
        self.assertIsNotNone(self.agent.policy_net)
        self.assertIsNotNone(self.agent.value_net)
        
        # 检查优化器
        self.assertIsNotNone(self.agent.policy_optimizer)
        self.assertIsNotNone(self.agent.value_optimizer)
    
    def test_select_action(self):
        """测试选择动作"""
        # 选择动作
        action, action_info = self.agent.select_action(self.state)
        
        # 检查动作是否在有效范围内
        self.assertGreaterEqual(action, 0)
        self.assertLess(action, self.action_dim)
        
        # 检查动作信息
        self.assertIn('log_prob', action_info)
        self.assertIn('value', action_info)
        
        # 检查数据类型
        self.assertIsInstance(action, int)
        self.assertIsInstance(action_info['log_prob'], float)
        self.assertIsInstance(action_info['value'], float)
    
    def test_update(self):
        """测试更新"""
        # 更新智能体
        update_info = self.agent.update(
            states=self.states,
            actions=self.actions,
            rewards=self.rewards,
            next_states=self.next_states,
            dones=self.dones
        )
        
        # 检查更新信息
        self.assertIn('policy_loss', update_info)
        self.assertIn('value_loss', update_info)
        self.assertIn('entropy', update_info)
        
        # 检查数据类型
        self.assertIsInstance(update_info['policy_loss'], float)
        self.assertIsInstance(update_info['value_loss'], float)
        self.assertIsInstance(update_info['entropy'], float)
    
    def test_compute_returns(self):
        """测试计算回报"""
        # 创建一些示例数据
        rewards = torch.tensor([1.0, 2.0, 3.0, 4.0])
        values = torch.tensor([0.5, 1.5, 2.5, 3.5])
        next_value = torch.tensor(4.5)
        dones = torch.tensor([False, False, False, True])
        
        # 计算回报
        returns = self.agent._compute_returns(rewards, values, next_value, dones)
        
        # 检查回报形状
        self.assertEqual(returns.shape, rewards.shape)
        
        # 检查最后一个回报（应该等于最后一个奖励）
        self.assertAlmostEqual(returns[-1].item(), rewards[-1].item(), places=5)
    
    def test_compute_advantages(self):
        """测试计算优势"""
        # 创建一些示例数据
        returns = torch.tensor([1.0, 2.0, 3.0, 4.0])
        values = torch.tensor([0.5, 1.5, 2.5, 3.5])
        
        # 计算优势
        advantages = self.agent._compute_advantages(returns, values)
        
        # 检查优势形状
        self.assertEqual(advantages.shape, returns.shape)
        
        # 检查优势计算是否正确
        expected_advantages = returns - values
        for i in range(len(advantages)):
            self.assertAlmostEqual(advantages[i].item(), expected_advantages[i].item(), places=5)
    
    def test_save_load(self):
        """测试保存和加载模型"""
        # 保存模型
        save_path = "test_model.pt"
        self.agent.save(save_path)
        
        # 创建一个新的智能体
        new_agent = PPOAgent(
            state_dim=self.state_dim,
            action_dim=self.action_dim,
            hidden_dims=self.hidden_dims
        )
        
        # 加载模型
        new_agent.load(save_path)
        
        # 检查两个智能体的参数是否相同
        for param1, param2 in zip(self.agent.policy_net.parameters(), new_agent.policy_net.parameters()):
            self.assertTrue(torch.allclose(param1, param2))
        
        for param1, param2 in zip(self.agent.value_net.parameters(), new_agent.value_net.parameters()):
            self.assertTrue(torch.allclose(param1, param2))
        
        # 删除测试文件
        import os
        if os.path.exists(save_path):
            os.remove(save_path)

if __name__ == '__main__':
    unittest.main()