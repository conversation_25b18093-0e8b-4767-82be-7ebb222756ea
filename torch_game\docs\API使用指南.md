# Torch Game API 使用指南

## 🎯 快速开始

### 1. 基本使用流程

```python
# 导入必要模块
from torch_game.core.env import OptimizedMatch3Env
from torch_game.core.agents import PPOAgent
from torch_game.core.train import Trainer

# 创建环境
env = OptimizedMatch3Env(
    game_assistant=game_assistant,  # s4_三消.py的实例
    max_steps=150,
    n_colors=19,
    storage_capacity=7
)

# 创建智能体
agent = PPOAgent(
    state_dim=env.observation_space.shape[0],
    action_dim=env.action_space.n,
    lr=3e-4
)

# 开始训练
trainer = Trainer(env=env, agent=agent, config=config)
trainer.train()
```

### 2. 配置文件使用

```python
# 使用配置文件训练
python examples/train_with_config.py --config configs/optimized_game.yaml

# 覆盖配置参数
python examples/train_with_config.py --config configs/optimized_game.yaml \
    --override "env.max_steps=200" "agent.lr=1e-4"
```

## 🎮 环境 API

### OptimizedMatch3Env

优化的三消游戏环境，集成智能状态管理。

#### 初始化参数

```python
OptimizedMatch3Env(
    game_assistant=None,      # 游戏助手实例
    max_steps=150,           # 每回合最大步数
    n_colors=19,             # 方块类型数量
    storage_capacity=7       # 预存区容量
)
```

#### 主要方法

```python
# 重置环境
observation = env.reset()

# 执行动作
observation, reward, done, info = env.step(action)

# 渲染环境状态
env.render(mode='human')

# 关闭环境
env.close()
```

#### 观察空间

```python
# 观察空间结构
observation_space = Box(
    low=0.0, high=1.0,
    shape=(total_size,),  # 主区域统计 + 预存区状态 + 额外信息
    dtype=np.float32
)

# 观察值组成
# - 主区域方块统计: [n_colors] 每种方块的归一化数量
# - 预存区状态: [storage_capacity * n_colors] one-hot编码
# - 额外信息: [5] 预存区使用比例、步数比例、连续失败等
```

#### 动作空间

```python
# 动作空间
action_space = Discrete(100)  # 最多100个可点击方块

# 动作映射
# action: int -> 方块索引 -> 具体方块 -> 游戏点击操作
```

#### 奖励机制

```python
# 奖励组成
rewards = {
    'invalid_action': -1.0,           # 无效动作惩罚
    'elimination': 5.0,               # 每消除一个方块
    'complete_elimination': 15.0,     # 完整消除奖励
    'storage_near_full': -2.0,        # 预存区接近满
    'storage_full': -5.0,             # 预存区满
    'potential_elimination': 1.0,     # 潜在消除机会
    'main_area_clear': 0.5           # 主区域清理
}
```

## 🤖 智能体 API

### PPOAgent

基于PPO算法的强化学习智能体。

#### 初始化参数

```python
PPOAgent(
    state_dim,               # 状态维度
    action_dim,              # 动作维度
    lr=3e-4,                # 学习率
    gamma=0.99,              # 折扣因子
    eps_clip=0.2,            # PPO裁剪参数
    k_epochs=4,              # 更新轮数
    entropy_coef=0.01,       # 熵系数
    value_coef=0.5           # 价值函数系数
)
```

#### 主要方法

```python
# 选择动作
action, log_prob = agent.select_action(state)

# 确定性动作选择（评估时使用）
action = agent.select_action(state, deterministic=True)

# 获取状态价值
value = agent.get_value(state)

# 更新网络
loss_info = agent.update(batch_data)

# 保存/加载模型
agent.save('model.pth')
agent.load('model.pth')
```

## 🏋️ 训练 API

### Trainer

统一的训练框架。

#### 初始化参数

```python
Trainer(
    env,                     # 环境实例
    agent,                   # 智能体实例
    config                   # 训练配置
)
```

#### 主要方法

```python
# 开始训练
trainer.train()

# 评估智能体
avg_reward, avg_length = trainer.evaluate(num_episodes=10)

# 保存模型
trainer.save_model('best_model.pth')

# 加载模型
trainer.load_model('best_model.pth')
```

#### 训练配置

```yaml
training:
  total_episodes: 1000      # 总训练回合数
  save_interval: 50         # 模型保存间隔
  eval_interval: 25         # 评估间隔
  max_episode_length: 150   # 最大回合长度
  
  early_stopping:
    enabled: true
    patience: 100           # 早停耐心值
    min_improvement: 1.0    # 最小改善阈值
```

## 🔧 工具 API

### 配置管理

```python
from torch_game.core.utils import ConfigManager

# 加载配置
config = ConfigManager.load('configs/optimized_game.yaml')

# 获取配置值
max_steps = config.get('env.max_steps', 150)

# 覆盖配置
config.override({'env.max_steps': 200, 'agent.lr': 1e-4})
```

### 日志记录

```python
from torch_game.core.utils import setup_logging

# 设置日志
setup_logging(level='INFO', save_logs=True)

# 记录训练指标
logger.info(f"Episode {episode}: Reward={reward:.2f}")
```

## 📊 评估 API

### 性能评估

```python
from torch_game.core.evaluate import Evaluator

# 创建评估器
evaluator = Evaluator(env, agent)

# 评估性能
results = evaluator.evaluate(
    num_episodes=100,
    render=False,
    save_videos=False
)

# 评估结果
print(f"平均奖励: {results['avg_reward']:.2f}")
print(f"平均长度: {results['avg_length']:.2f}")
print(f"成功率: {results['success_rate']:.2%}")
```

## 🎯 示例代码

### 完整训练示例

```python
import sys
import os
sys.path.append('path/to/s4_三消.py')

from torch_game.core.env import OptimizedMatch3Env
from torch_game.core.agents import PPOAgent
from torch_game.core.train import Trainer
from torch_game.core.utils import setup_logging, ConfigManager
from s4_三消 import TripleEliminationGameAssistant
from assist import AssistBasicToolkit

def main():
    # 设置日志
    setup_logging()
    
    # 加载配置
    config = ConfigManager.load('configs/optimized_game.yaml')
    
    # 创建游戏助手
    assist = AssistBasicToolkit()
    hwnd = assist.register_and_bind_window_objects('最强祖师')
    assist.detection.load_model('models/sanxiao/best.pt')
    game_assistant = TripleEliminationGameAssistant.created(hwnd, assist)
    
    # 创建环境
    env = OptimizedMatch3Env(
        game_assistant=game_assistant,
        **config.get('env', {})
    )
    
    # 创建智能体
    agent = PPOAgent(
        state_dim=env.observation_space.shape[0],
        action_dim=env.action_space.n,
        **config.get('agent', {})
    )
    
    # 创建训练器
    trainer = Trainer(env=env, agent=agent, config=config.get('training', {}))
    
    # 开始训练
    trainer.train()

if __name__ == "__main__":
    main()
```

### 评估示例

```python
def evaluate_trained_agent():
    # 加载训练好的模型
    agent.load('models/best_model.pth')
    
    # 评估性能
    total_reward = 0
    num_episodes = 10
    
    for episode in range(num_episodes):
        state = env.reset()
        episode_reward = 0
        done = False
        
        while not done:
            action = agent.select_action(state, deterministic=True)
            state, reward, done, info = env.step(action)
            episode_reward += reward
            
            # 可选：渲染游戏过程
            env.render()
        
        total_reward += episode_reward
        print(f"Episode {episode + 1}: Reward = {episode_reward:.2f}")
    
    avg_reward = total_reward / num_episodes
    print(f"平均奖励: {avg_reward:.2f}")
```

## 🔍 调试技巧

### 1. 环境调试

```python
# 检查观察空间
print(f"观察空间: {env.observation_space}")
print(f"动作空间: {env.action_space}")

# 检查状态
state = env.reset()
print(f"状态形状: {state.shape}")
print(f"状态范围: [{state.min():.3f}, {state.max():.3f}]")
```

### 2. 智能体调试

```python
# 检查动作选择
action, log_prob = agent.select_action(state)
print(f"选择动作: {action}, 对数概率: {log_prob:.3f}")

# 检查状态价值
value = agent.get_value(state)
print(f"状态价值: {value:.3f}")
```

### 3. 训练调试

```python
# 监控训练指标
def on_episode_end(episode, reward, length, info):
    print(f"Episode {episode}: Reward={reward:.2f}, Length={length}")
    if info.get('action_success_rate'):
        print(f"  动作成功率: {info['action_success_rate']:.2%}")

# 设置回调
trainer.set_callback('episode_end', on_episode_end)
```

## 📝 最佳实践

1. **环境配置**: 根据游戏特点调整 `max_steps` 和 `storage_capacity`
2. **奖励设计**: 平衡即时奖励和长期策略
3. **网络架构**: 根据状态复杂度调整隐藏层大小
4. **训练策略**: 使用课程学习，从简单到复杂
5. **评估频率**: 定期评估避免过拟合
6. **模型保存**: 保存最佳模型和最新模型
7. **日志记录**: 详细记录训练过程便于分析

## 🚀 性能优化

1. **批量处理**: 使用批量更新提高训练效率
2. **并行环境**: 多环境并行收集数据
3. **经验回放**: 重复使用收集的经验
4. **网络优化**: 使用适当的激活函数和归一化
5. **超参数调优**: 系统性调优学习率、批量大小等参数
