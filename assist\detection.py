"""
目标检测相关接口封装
"""
from ultralytics import YOLO

class Detection:
    def __init__(self):
        """
        初始化检测器，加载YOLOv8模型
        :param model_path: 模型权重路径
        :param device: 推理设备，默认'cuda'
        """
        self.model: YOLO = None
        self.device = None

    def load_model(self, model_path='', device='cuda'):
        """
        加载模型
        :param model_path: 模型权重路径
        :param device: 推理设备，默认'cuda'
        """
        if not model_path:
            raise ValueError("model_path is empty")

        self.model = YOLO(model_path, task='detect')
        self.model.fuse()
        self.device = device

    def detect_objects(self, image, conf=0.5, imgsz=640, max_det=100):
        """
        对图片进行目标检测，返回第一个Results对象
        :param image: 输入图片（numpy数组或PIL.Image）
        :param conf: 置信度阈值
        :param imgsz: 输入图片尺寸
        :param max_det: 最大检测目标数
        :return: Results对象
        """
        results = self.model.predict(
            image,
            imgsz=imgsz,
            conf=conf,
            max_det=max_det,
            device=self.device
        )
        return results[0]
    
    def detect_targets(self, image, save=False, show=False, project='', name='predict', conf=0.5, imgsz=640, max_det=10):
        """
        对图片进行目标检测，返回第一个Results对象
        :param image: 输入图片（numpy数组或PIL.Image）
        :param save: 是否保存检测结果
        :param project: 保存路径
        :param name: 保存文件名
        :param conf: 置信度阈值
        """
        if save:
            results = self.model(
                image, 
                save=save, 
                show=show,
                project=project, 
                name=name, conf=conf, 
                imgsz=imgsz, 
                max_det=max_det, 
                device=self.device
            )
        else:
            results = self.model(
                image, conf=conf, imgsz=imgsz, max_det=max_det, device=self.device
            )
        return results[0]

