# 优化三消游戏强化学习配置
# 集成了s4_三消.py的智能状态管理

# 环境配置
env:
  type: "OptimizedMatch3Env"
  max_steps: 150                # 每回合最大步数
  n_colors: 19                  # 方块类型数量（支持更多类型）
  storage_capacity: 7           # 预存区容量
  
# 智能体配置
agent:
  type: "PPOAgent"
  lr: 3e-4                     # 学习率
  gamma: 0.99                  # 折扣因子
  eps_clip: 0.2                # PPO裁剪参数
  k_epochs: 4                  # PPO更新轮数
  entropy_coef: 0.01           # 熵系数
  value_coef: 0.5              # 价值函数系数
  
  # 网络结构
  hidden_dims: [256, 128, 64]  # 隐藏层维度
  activation: "relu"           # 激活函数
  
# 训练配置
training:
  total_episodes: 1000         # 总训练回合数
  save_interval: 50            # 模型保存间隔
  eval_interval: 25            # 评估间隔
  max_episode_length: 150      # 最大回合长度
  
  # 早停配置
  early_stopping:
    enabled: true
    patience: 100              # 连续多少回合无改善则停止
    min_improvement: 1.0       # 最小改善阈值
    
  # 学习率调度
  lr_scheduler:
    enabled: true
    type: "StepLR"
    step_size: 200             # 每200回合降低学习率
    gamma: 0.8                 # 学习率衰减因子

# 奖励配置
reward:
  # 基础奖励
  invalid_action_penalty: -1.0      # 无效动作惩罚
  elimination_reward: 5.0           # 每消除一个方块的奖励
  complete_elimination_bonus: 15.0  # 完整消除（3个或更多）奖励
  
  # 预存区管理
  storage_near_full_penalty: -2.0   # 预存区接近满（6个）惩罚
  storage_full_penalty: -5.0        # 预存区满（7个）惩罚
  potential_elimination_reward: 1.0  # 有潜在消除机会奖励
  
  # 主区域清理
  main_area_clear_reward: 0.5       # 清理主区域方块奖励

# 游戏助手配置
game_assistant:
  window_name: "最强祖师"           # 游戏窗口名称
  model_path: "models/sanxiao/best.pt"  # YOLO模型路径
  
  # 区域配置
  main_area: [285, 100, 1230, 625]      # 主游戏区域坐标
  storage_area: [405, 750, 1185, 895]   # 预存区域坐标
  
  # 状态检测配置
  state_detection:
    check_interval: 0.2              # 状态检测间隔（秒）
    max_wait_seconds: 10             # 最大等待时间
    
# 日志配置
logging:
  level: "INFO"
  save_logs: true
  log_dir: "logs"
  
  # 训练指标记录
  metrics:
    - "episode_reward"
    - "episode_length"
    - "action_success_rate"
    - "elimination_count"
    - "storage_usage"
    
# 模型保存配置
model:
  save_dir: "models/optimized_match3"
  save_best: true                    # 保存最佳模型
  save_latest: true                  # 保存最新模型
  
# 评估配置
evaluation:
  episodes: 10                       # 评估回合数
  render: false                      # 是否渲染评估过程
  save_videos: false                 # 是否保存评估视频
  
# 实验配置
experiment:
  name: "optimized_match3_v1"        # 实验名称
  description: "使用优化状态管理的三消游戏强化学习"
  tags: ["match3", "optimized", "ppo", "real_game"]
