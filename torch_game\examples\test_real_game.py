"""
真实游戏环境测试脚本
逐步验证与真实游戏的连接和交互
"""

import os
import sys
import time
import numpy as np

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

print("🎮 真实游戏环境测试")
print("=" * 50)

def test_game_assistant_connection():
    """测试游戏助手连接"""
    print("\n📋 步骤1: 测试游戏助手连接")
    
    try:
        from s4_三消 import TripleEliminationGameAssistant
        from assist import AssistBasicToolkit
        print("✅ 游戏助手模块导入成功")
        
        # 创建基础工具包
        assist = AssistBasicToolkit()
        print("✅ AssistBasicToolkit创建成功")
        
        # 尝试连接游戏窗口
        game_window = "最强祖师"
        print(f"🔍 尝试连接游戏窗口: {game_window}")
        
        hwnd = assist.register_and_bind_window_objects(game_window)
        print(f"✅ 游戏窗口连接成功: hwnd={hwnd}")
        
        # 检查模型文件
        model_path = r"models\sanxiao\best.pt"
        print(f"🔍 检查模型文件: {model_path}")
        
        if os.path.exists(model_path):
            print("✅ 模型文件存在")
            
            # 加载YOLO模型
            assist.detection.load_model(model_path)
            print("✅ YOLO模型加载成功")
            
            # 创建游戏助手
            game_assistant = TripleEliminationGameAssistant.created(hwnd, assist)
            print("✅ 游戏助手创建成功")
            
            return game_assistant
            
        else:
            print(f"❌ 模型文件不存在: {model_path}")
            return None
            
    except Exception as e:
        print(f"❌ 游戏助手连接失败: {e}")
        import traceback
        traceback.print_exc()
        return None


def test_game_state_capture(game_assistant):
    """测试游戏状态捕获"""
    print("\n📋 步骤2: 测试游戏状态捕获")
    
    if game_assistant is None:
        print("❌ 游戏助手未连接，跳过测试")
        return False
    
    try:
        # 截图测试
        print("📸 尝试截图...")
        current_img = game_assistant.screenshot.capture_client_area()
        
        if current_img is None:
            print("❌ 截图失败")
            return False
        
        print(f"✅ 截图成功: 尺寸={current_img.size}")
        
        # 区域裁剪测试
        print("✂️ 测试区域裁剪...")
        main_img = current_img.crop(game_assistant.main_area)
        storage_img = current_img.crop(game_assistant.storage_area)
        
        print(f"✅ 主区域裁剪成功: 尺寸={main_img.size}")
        print(f"✅ 预存区裁剪成功: 尺寸={storage_img.size}")
        
        # YOLO检测测试
        print("🔍 测试YOLO检测...")
        main_results = game_assistant.detection.detect_objects(main_img)
        storage_results = game_assistant.detection.detect_objects(storage_img)
        
        print(f"✅ 主区域检测完成: 检测到{len(main_results)}个对象")
        print(f"✅ 预存区检测完成: 检测到{len(storage_results)}个对象")
        
        # 方块分析测试
        print("📊 测试方块分析...")
        main_blocks = game_assistant.analyze_blocks(main_results)
        storage_blocks = game_assistant.analyze_blocks(storage_results)
        
        main_count = sum(len(blocks) for blocks in main_blocks.values())
        storage_count = sum(len(blocks) for blocks in storage_blocks.values())
        
        print(f"✅ 主区域方块分析完成: {main_count}个方块")
        print(f"✅ 预存区方块分析完成: {storage_count}个方块")
        
        # 显示详细信息
        print("\n📋 检测详情:")
        for cls_id, blocks in main_blocks.items():
            if blocks:
                print(f"   主区域类型{cls_id}: {len(blocks)}个方块")
        
        for cls_id, blocks in storage_blocks.items():
            if blocks:
                print(f"   预存区类型{cls_id}: {len(blocks)}个方块")
        
        return True
        
    except Exception as e:
        print(f"❌ 游戏状态捕获失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_click_match3_env(game_assistant):
    """测试ClickMatch3Env环境"""
    print("\n📋 步骤3: 测试ClickMatch3Env环境")
    
    try:
        from torch_game.core.env import ClickMatch3Env
        
        # 创建环境
        print("🏗️ 创建ClickMatch3Env...")
        env = ClickMatch3Env(
            game_assistant=game_assistant,
            max_steps=10,
            n_colors=6,
            storage_capacity=7
        )
        print(f"✅ 环境创建成功")
        print(f"   观察空间: {env.observation_space.shape}")
        print(f"   动作空间: {env.action_space.n}")
        
        # 测试环境重置
        print("🔄 测试环境重置...")
        observation = env.reset()
        print(f"✅ 环境重置成功")
        print(f"   观察值形状: {observation.shape}")
        print(f"   观察值范围: [{observation.min():.3f}, {observation.max():.3f}]")
        print(f"   非零元素: {np.count_nonzero(observation)}")
        
        # 测试几步动作
        print("🎮 测试动作执行...")
        total_reward = 0
        
        for step in range(3):
            # 随机选择动作
            action = np.random.randint(0, env.action_space.n)
            
            print(f"   步骤{step+1}: 执行动作{action}")
            observation, reward, done, info = env.step(action)
            total_reward += reward
            
            print(f"      奖励: {reward:.2f}")
            print(f"      结束: {done}")
            print(f"      信息: {info}")
            
            if done:
                print("      游戏结束")
                break
            
            # 等待一下，避免操作过快
            time.sleep(1)
        
        print(f"✅ 动作测试完成，总奖励: {total_reward:.2f}")
        
        # 关闭环境
        env.close()
        print("✅ 环境关闭成功")
        
        return True
        
    except Exception as e:
        print(f"❌ ClickMatch3Env测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_ppo_agent_integration(game_assistant):
    """测试PPO智能体集成"""
    print("\n📋 步骤4: 测试PPO智能体集成")
    
    try:
        from torch_game.core.env import ClickMatch3Env
        from torch_game.core.agents import PPOAgent
        
        # 创建环境和智能体
        env = ClickMatch3Env(
            game_assistant=game_assistant,
            max_steps=5,
            n_colors=6,
            storage_capacity=7
        )
        
        agent = PPOAgent(
            state_dim=env.observation_space.shape[0],
            action_dim=env.action_space.n,
            lr=3e-4,
            gamma=0.99,
            epsilon=0.2,
            hidden_dim=128
        )
        
        print("✅ 环境和智能体创建成功")
        
        # 测试智能体与环境交互
        print("🤖 测试智能体决策...")
        state = env.reset()
        
        for step in range(3):
            # 智能体选择动作
            action_result = agent.select_action(state)
            if isinstance(action_result, tuple):
                action, log_prob = action_result
            else:
                action = action_result
                log_prob = 0.0
            
            print(f"   步骤{step+1}: 智能体选择动作{action}")
            
            # 执行动作
            state, reward, done, info = env.step(action)
            print(f"      奖励: {reward:.2f}, 结束: {done}")
            
            if done:
                break
            
            time.sleep(1)
        
        env.close()
        print("✅ PPO智能体集成测试成功")
        
        return True
        
    except Exception as e:
        print(f"❌ PPO智能体集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("🚀 开始真实游戏环境完整测试")
    print("\n⚠️  请确保:")
    print("   1. '最强祖师'游戏正在运行")
    print("   2. 游戏处于可操作状态")
    print("   3. 游戏窗口可见且未被遮挡")
    
    input("\n按Enter键继续...")
    
    # 步骤1: 测试游戏助手连接
    game_assistant = test_game_assistant_connection()
    
    if game_assistant is None:
        print("\n❌ 游戏助手连接失败，无法继续测试")
        print("\n💡 请检查:")
        print("   - 游戏是否正在运行")
        print("   - 游戏窗口标题是否为'最强祖师'")
        print("   - 是否有足够的权限访问游戏窗口")
        return
    
    # 步骤2: 测试游戏状态捕获
    if not test_game_state_capture(game_assistant):
        print("\n❌ 游戏状态捕获失败，无法继续测试")
        return
    
    # 步骤3: 测试ClickMatch3Env环境
    if not test_click_match3_env(game_assistant):
        print("\n❌ ClickMatch3Env测试失败，无法继续测试")
        return
    
    # 步骤4: 测试PPO智能体集成
    if not test_ppo_agent_integration(game_assistant):
        print("\n❌ PPO智能体集成测试失败")
        return
    
    print("\n🎉 所有测试通过！")
    print("✅ 真实游戏环境已准备就绪")
    print("\n🚀 现在可以运行完整的训练:")
    print("   python torch_game\\examples\\train_real_game_fixed.py --game_window \"最强祖师\" --model_path \"models\\sanxiao\\best.pt\"")


if __name__ == "__main__":
    main()
