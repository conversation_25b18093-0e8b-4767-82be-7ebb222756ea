

from enum import Enum

class State(Enum):
    '''
    任务状态机
    '''
    PENDING = "pending"    # 待执行
    RUNNING = "running"    # 执行中
    FINISHED = "finished"  # 已完成
    FAILED = "failed"      # 已失败

    @staticmethod
    def is_pending(state) -> bool:
        '''
        检查任务是否处于待执行状态
        '''
        return state == State.PENDING

    @staticmethod
    def is_running(state) -> bool:
        '''
        检查任务是否正在执行
        '''
        return state == State.RUNNING

    @staticmethod
    def is_finished(state) -> bool:
        '''
        检查任务是否已完成
        '''
        return state == State.FINISHED

    @staticmethod
    def is_failed(state) -> bool:
        '''
        检查任务是否已失败
        '''
        return state == State.FAILED

    @staticmethod
    def is_completed(state) -> bool:
        '''
        检查任务是否已完成或失败
        '''
        return State.is_finished(state) or State.is_failed(state)

    @staticmethod
    def is_available(state) -> bool:
        '''
        检查任务是否可用
        '''
        pending = State.is_pending(state)
        running = State.is_running(state)
        failed = State.is_failed(state)
        finished = State.is_finished(state)
        return pending or running or failed or finished

