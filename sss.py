import time
s1 = time.time()
s2 = time.strftime("%Y%m%d%H%M%S", time.localtime(s1))
print(s2)

# print(2 % 3)


import random

print(random.randint(1, 3))

# l = [[x, i, j] for x, i, j in enumerate(zip([1, 2, 3], [4, 5, 6]), 7)]
# print(l)

print(f"1 % 3 = {1 % 3}")
print(f"2 % 3 = {2 % 3}")
print(f"3 % 3 = {3 % 3}")
print(f"4 % 3 = {4 % 3}")
print(f"5 % 3 = {5 % 3}")
print(f"6 % 3 = {6 % 3}")
print(f"7 % 3 = {7 % 3}")

print(f"1 // 3 = {1 // 3}")
print(f"2 // 3 = {2 // 3}")
print(f"3 // 3 = {3 // 3}")
print(f"4 // 3 = {4 // 3}")
print(f"5 // 3 = {5 // 3}")
print(f"6 // 3 = {6 // 3}")
print(f"7 // 3 = {7 // 3}")