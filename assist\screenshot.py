"""
截图相关接口封装（高性能，参考ImageController）
"""
import cv2
import dxcam
import numpy as np
from PIL import Image
from typing import Tuple, Union

from assist.window_ops import WindowOps

class Screenshot:
    """
    高性能截图与录制工具类，适用于窗口客户区截图、区域截图、视频录制等场景。
    依赖dxcam库，底层窗口操作全部通过WindowOps实现。
    """
    def __init__(self, hwnd: int):

        self.hwnd = hwnd
        self.window = WindowOps()
        self.screen_w, self.screen_h = self.window.get_screen_size()
        self.camera_rect = self.get_window_rect()

        try:
            self.camera = dxcam.create(
                output_color="BGR",
                output_idx=0,  # 默认显示器
                max_buffer_len=64  # 帧缓冲区大小
            )
            self.is_recording = False
            self.target_fps = 30  # 默认30fps
            self.video_writer = None  # 视频写入器
        except Exception as e:
            raise RuntimeError(f"截图初始化失败: {str(e)}")

    def get_window_rect(self) -> <PERSON><PERSON>[int, int, int, int]:
        """获取窗口的屏幕坐标，自动裁剪到屏幕范围"""
        left, top, right, bottom = self.window.get_window_rect(self.hwnd)
        left = max(0, left)
        top = max(0, top)
        right = min(self.screen_w, right)
        bottom = min(self.screen_h, bottom)
        return (left, top, right, bottom)

    def _create_video_writer(self, output_path, rect, fps):
        '''创建视频写入器'''

        # 如果指定了输出路径，创建视频写入器
        if not output_path:
            return

        width = rect[2] - rect[0]
        height = rect[3] - rect[1]
        # 使用 XVID 编码器，这是一个更通用的编码器
        fourcc = cv2.VideoWriter_fourcc(*'XVID')
        # 设置视频文件扩展名为.avi
        if not output_path.lower().endswith('.avi'):
            output_path = output_path.rsplit('.', 1)[0] + '.avi'
        self.video_writer = cv2.VideoWriter(
            output_path,
            fourcc,
            fps,
            (width, height)
        )

    def _release_video_writer(self):
        '''释放视频写入器'''
        if self.video_writer:
            self.video_writer.release()
            self.video_writer = None

    def start_recording(self, target_fps: int = 30, output_path: str = ""):
        '''开始视频录制'''
        if self.is_recording:
            return
            
        try:
            # 使用当前窗口位置和大小启动视频录制
            rect = self.get_window_rect()
            self.camera.start(
                target_fps=target_fps,
                video_mode=True,
                region=rect
            )
            
            # 如果指定了输出路径，创建视频写入器
            self._create_video_writer(output_path, rect, target_fps)
            
            # 只有在成功启动后才设置状态
            self.is_recording = True
            self.target_fps = target_fps
        except Exception as e:
            self._release_video_writer()
            print(f"启动录制失败: {str(e)}")
            
    def stop_recording(self):
        '''停止视频录制'''
        if self.is_recording:
            self.camera.stop()
            self._release_video_writer()
            self.is_recording = False

    def get_frame(self) -> Union[np.ndarray, None]:
        '''获取最新帧'''
        try:
            # 检查并更新视频录制区域
            self.update_recording_region()

            # 从视频流中获取最新帧
            frame = self.camera.get_latest_frame()
            if frame is None or frame.size == 0:
                return

            return frame
        except Exception as e:
            print(f"获取帧失败: {str(e)}")

    def write_frame(self, frame: np.ndarray) -> None:
        '''将帧写入视频'''
        if self.is_recording and self.video_writer:
            self.video_writer.write(frame)

    def update_recording_region(self) -> bool:
        """检测窗口位置是否发生变化，并自动更新dxcam录制区域"""
        if not self.is_recording:
            return False

        # 获取当前窗口位置和大小
        current_rect = self.get_window_rect()

        # 检查窗口是否移动或改变大小
        if current_rect != self.camera_rect:

            # 更新录制区域
            self.camera_rect = current_rect

            # 停止并重新启动dxcam
            self.camera.stop()
            self.camera.start(
                target_fps=self.target_fps,
                video_mode=True,
                region=current_rect
            )
            return True
        return False

    def calculate_region(self, region: Tuple[int, int, int, int]) -> Tuple[int, int, int, int]:
        '''计算区域的绝对坐标'''
        try:
            # 获取客户区域坐标
            client_left, client_top = self.window.client_to_screen(self.hwnd, (0, 0))
            
            # 计算绝对坐标
            abs_left = max(0, client_left + region[0])
            abs_top = max(0, client_top + region[1])
            abs_right = min(self.screen_w, client_left + region[2])
            abs_bottom = min(self.screen_h, client_top + region[3])

            # 确保区域有效
            if abs_right <= abs_left or abs_bottom <= abs_top:
                print("无效的区域范围")
                return (0, 0, 0, 0)
            
            return (abs_left, abs_top, abs_right, abs_bottom)
        except Exception as e:
            print(f"计算区域坐标失败: {str(e)}")
            return (0, 0, 0, 0)

    def capture_client_area(self) -> Image.Image:
        '''截取窗口客户区'''
        try:
            # 如果正在录制视频，使用get_latest_frame方法获取最新帧
            if self.is_recording:
                frame = self.camera.get_latest_frame()
                if frame is None or frame.size == 0:
                    return
                # 转换为PIL格式
                return Image.fromarray(cv2.cvtColor(frame, cv2.COLOR_BGR2RGB))

            # 使用dxcam高性能截图，指定区域
            frame = self.camera.grab(region=self.get_window_rect())
            if frame is None or frame.size == 0:
                return 
            # 转换为PIL格式
            return Image.fromarray(cv2.cvtColor(frame, cv2.COLOR_BGR2RGB))
        except Exception as e:
            raise RuntimeError(f"客户区截图失败: {str(e)}")

    def capture_region(self, region: Tuple[int, int, int, int]) -> Union[Image.Image, None]:
        """截取窗口客户区指定区域，录制时优先用最新帧，否则用dxcam截图，返回PIL.Image"""
        try:
            if self.is_recording:
                frame = self.camera.get_latest_frame()
                if frame is None or frame.size == 0:
                    return
                # 获取窗口位置和大小
                window_rect = self.get_window_rect()
                # 获取窗口客户区的位置
                client_left, client_top = self.window.client_to_screen(self.hwnd, (0, 0))

                # 计算区域在录制帧中的相对位置
                rel_left = client_left + region[0] - window_rect[0]
                rel_top = client_top + region[1] - window_rect[1]
                rel_right = client_left + region[2] - window_rect[0]
                rel_bottom = client_top + region[3] - window_rect[1]

                # 确保坐标在有效范围内
                rel_left = max(0, rel_left)
                rel_top = max(0, rel_top)
                rel_right = min(frame.shape[1], rel_right)
                rel_bottom = min(frame.shape[0], rel_bottom)

                # 从帧中截取指定区域
                region_frame = frame[rel_top:rel_bottom, rel_left:rel_right]

                # 转换为PIL格式
                return Image.fromarray(cv2.cvtColor(region_frame, cv2.COLOR_BGR2RGB))

            # 使用dxcam高性能截图，指定区域
            frame = self.camera.grab(region=self.calculate_region(region))
            if frame is None or frame.size == 0:
                return
            
            # 转换为PIL格式
            return Image.fromarray(cv2.cvtColor(frame, cv2.COLOR_BGR2RGB))
        except Exception as e:
            raise RuntimeError(f"区域截图失败: {str(e)}")

    def close(self):
        """释放资源"""
        try:
            if self.is_recording:
                self.stop_recording()
            if hasattr(self, 'camera') and self.camera is not None:
                self.camera.stop()
                self.camera = None
        except Exception as e:
            print(f"释放资源失败: {str(e)}")