'''
鼠标管理器
'''

import pyautogui


class Mouse:

    # 鼠标信息获取
    def get_cursor_pos(self):
        '''
        获取鼠标位置
        '''
        return pyautogui.position()

    # 鼠标操作
    def left_click(self):
        '''
        鼠标左键单击
        '''
        pyautogui.click()

    def left_double_click(self):
        '''
        鼠标左键双击
        '''
        pyautogui.doubleClick()

    def right_click(self):
        '''
        鼠标右键单击
        '''
        pyautogui.rightClick()

    def move_to(self, x, y):
        '''
        鼠标移动到指定位置
        '''
        pyautogui.moveTo(x, y, duration=0.25)

    def move_rel(self, x, y):
        '''
        鼠标相对移动
        '''
        pyautogui.moveRel(x, y)

    def drag_to(self, x, y):
        '''
        鼠标拖拽到指定位置
        '''
        pyautogui.dragTo(x, y)

