"""
简单测试脚本：验证模拟模式是否正常工作
"""

import os
import sys
import numpy as np

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

print("🔍 开始简单测试...")
print(f"📁 当前工作目录: {os.getcwd()}")
print(f"📁 Python路径: {sys.path[-1]}")

try:
    print("📦 导入ClickMatch3Env...")
    from torch_game.core.env import ClickMatch3Env
    print("✅ ClickMatch3Env导入成功")
    
    print("📦 导入PPOAgent...")
    from torch_game.core.agents import PPOAgent
    print("✅ PPOAgent导入成功")
    
    print("🏗️ 创建环境...")
    env = ClickMatch3Env(
        game_assistant=None,  # 模拟模式
        max_steps=10,
        n_colors=6,
        storage_capacity=7
    )
    print(f"✅ 环境创建成功: 观察空间{env.observation_space.shape}, 动作空间{env.action_space.n}")
    
    print("🤖 创建智能体...")
    state_dim = env.observation_space.shape[0]
    action_dim = env.action_space.n
    
    agent = PPOAgent(
        state_dim=state_dim,
        action_dim=action_dim,
        lr=3e-4,
        gamma=0.99,
        epsilon=0.2,
        hidden_dim=128
    )
    print(f"✅ 智能体创建成功: 状态维度{state_dim}, 动作维度{action_dim}")
    
    print("🎮 测试一个简单的episode...")
    state = env.reset()
    print(f"   初始状态形状: {state.shape}")
    
    total_reward = 0
    for step in range(5):
        # 随机动作
        action = np.random.randint(0, action_dim)
        state, reward, done, info = env.step(action)
        total_reward += reward
        print(f"   步骤{step+1}: 动作={action}, 奖励={reward:.2f}, 结束={done}")
        
        if done:
            break
    
    print(f"✅ Episode完成: 总奖励={total_reward:.2f}")
    
    print("🧪 测试智能体动作选择...")
    state = env.reset()
    action = agent.select_action(state)
    if isinstance(action, tuple):
        action = action[0]
    print(f"✅ 智能体选择动作: {action}")
    
    env.close()
    print("🎉 所有测试通过!")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
