
import traceback
from utils import Window
from navigation import MapNavigator


if __name__ == "__main__":
    window = Window()
    hwnd = window.find_window_by_partial_title("大话西游2经典版 $Revision：")
    if not hwnd:
        print("未找到大话西游2窗口")
        exit()

    window.set_window_foreground(hwnd)
    
    navigator = MapNavigator(hwnd)

    try:
        target1 = (40, 40)
        target2 = (155, 15)
        navigator.navigate_to("东海渔村", target1)
        # position = navigator._get_current_position()
        # print(f"当前位置: {position}")
    except Exception as e:
        traceback.print_exc()
        print(f"地图导航失败：{e}")

    '''东海渔村:
  地图坐标: ((0, 0), (279, 164))
  屏幕坐标: ((216, 946), (1263, 331))
    屏幕坐标: ((186, 946), (1241, 321))

'''
