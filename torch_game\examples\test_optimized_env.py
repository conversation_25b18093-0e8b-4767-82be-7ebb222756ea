"""
测试优化后的三消游戏环境
验证智能状态管理和环境功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import time
import numpy as np
from torch_game.core.env import OptimizedMatch3Env

# 添加s4_三消.py的路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from s4_三消 import TripleEliminationGameAssistant
from assist import AssistBasicToolkit


def create_game_assistant():
    """创建游戏助手实例"""
    try:
        print("正在初始化游戏助手...")
        assist = AssistBasicToolkit()
        
        # 注册并绑定窗口操作对象
        hwnd = assist.register_and_bind_window_objects('最强祖师')
        print(f"找到游戏窗口，句柄: {hwnd}")
        
        # 加载YOLO模型
        model_path = r"models\sanxiao\best.pt"
        if not os.path.exists(model_path):
            raise FileNotFoundError(f"模型文件不存在: {model_path}")
        
        assist.detection.load_model(model_path)
        print(f"YOLO模型加载成功: {model_path}")
        
        # 创建游戏助手
        assistant = TripleEliminationGameAssistant.created(hwnd, assist)
        print("游戏助手创建成功")
        
        return assistant
        
    except Exception as e:
        print(f"创建游戏助手失败: {e}")
        return None


def test_environment():
    """测试环境基本功能"""
    print("=" * 60)
    print("测试优化后的三消游戏环境")
    print("=" * 60)
    
    # 创建游戏助手
    game_assistant = create_game_assistant()
    if game_assistant is None:
        print("无法创建游戏助手，使用模拟模式测试")
    
    try:
        # 创建环境
        print("\n1. 创建环境...")
        env = OptimizedMatch3Env(
            game_assistant=game_assistant,
            max_steps=50,  # 测试用较短步数
            n_colors=19,
            storage_capacity=7
        )
        
        print(f"   观察空间: {env.observation_space.shape}")
        print(f"   动作空间: {env.action_space.n}")
        
        # 重置环境
        print("\n2. 重置环境...")
        obs = env.reset()
        print(f"   初始观察值形状: {obs.shape}")
        print(f"   初始观察值范围: [{obs.min():.3f}, {obs.max():.3f}]")
        
        # 测试几个步骤
        print("\n3. 测试环境步骤...")
        total_reward = 0
        
        for step in range(10):
            # 随机动作
            action = env.action_space.sample()
            
            # 执行动作
            obs, reward, done, info = env.step(action)
            total_reward += reward
            
            print(f"   步骤 {step + 1}:")
            print(f"     动作: {action}")
            print(f"     奖励: {reward:.2f}")
            print(f"     完成: {done}")
            print(f"     动作成功: {info.get('action_success', False)}")
            print(f"     主区域方块数: {info.get('main_blocks_count', 0)}")
            print(f"     预存区方块数: {info.get('storage_blocks_count', 0)}")
            print(f"     连续失败次数: {info.get('consecutive_failures', 0)}")
            
            # 渲染环境状态
            env.render()
            
            if done:
                print(f"   回合结束，总奖励: {total_reward:.2f}")
                break
            
            # 短暂等待
            time.sleep(0.5)
        
        print("\n4. 测试环境重置...")
        obs = env.reset()
        print(f"   重置后观察值形状: {obs.shape}")
        
        print("\n5. 测试完成！")
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 清理资源
        try:
            if 'env' in locals():
                env.close()
                print("环境已关闭")
            if game_assistant is not None:
                if hasattr(game_assistant, 'assist') and hasattr(game_assistant.assist, 'close'):
                    game_assistant.assist.close()
                    print("游戏助手已关闭")
        except Exception as e:
            print(f"清理资源时出错: {e}")


def test_state_management():
    """测试状态管理功能"""
    print("\n" + "=" * 60)
    print("测试智能状态管理功能")
    print("=" * 60)
    
    # 创建游戏助手
    game_assistant = create_game_assistant()
    if game_assistant is None:
        print("无法创建游戏助手，跳过状态管理测试")
        return
    
    try:
        print("\n1. 测试状态检测...")
        current_img = game_assistant.screenshot.capture_client_area()
        if current_img is not None:
            state, result = game_assistant.detect_current_game_state(current_img)
            print(f"   当前游戏状态: {state}")
            if result:
                print(f"   检测结果: {result}")
        
        print("\n2. 测试游戏准备检查...")
        is_ready = game_assistant.is_game_ready_for_play()
        print(f"   游戏是否准备就绪: {is_ready}")
        
        print("\n3. 测试智能状态管理...")
        success = game_assistant.ensure_game_ready()
        print(f"   确保游戏准备成功: {success}")
        
        print("\n状态管理测试完成！")
        
    except Exception as e:
        print(f"状态管理测试出错: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主测试函数"""
    print("开始测试优化后的三消游戏环境...")
    
    # 测试环境基本功能
    test_environment()
    
    # 测试状态管理功能
    test_state_management()
    
    print("\n" + "=" * 60)
    print("所有测试完成！")
    print("=" * 60)


if __name__ == "__main__":
    main()
