"""
微信OCR三种模式测试

本文件用于测试微信OCR的三种模式：
1. 同步模式 - WeChatSyncOCR
2. 任务模式 - WeChatTasksOCR
3. 异步模式 - WeChatAsyncOCR

参考batch_image_handle.py中的代码，但不修改原文件。

支持参数控制：
- limit: 控制一次性输入数量
- skip: 控制从第skip批开始
- stop: 控制终止于第几批
例如：limit=10, skip=1, stop=1 表示一次输入10张图片，从1-10张图片开始，终止于1-10张图片
"""

import os
import cv2
import time
import asyncio
import aiofiles
import aiofiles.os as aioos
import numpy as np
from wechatocr import engine, WeChatSyncOCR, WeChatTasksOCR, WeChatAsyncOCR

# 定义预处理函数（与原文件相同）
pos = (35, 90, 255, 145)

def func_timer(func):
    """函数计时装饰器"""
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        print(f"{func.__name__} 耗时: {end_time - start_time:.2f}秒")
        return result
    return wrapper

def preprocess_image(image):
    """图像预处理函数"""
    # 裁剪
    x, y, w, h = pos
    image = image[y:h, x:w]

    # 灰度化
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

    # 二值化
    _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

    # 将二值化图像中的白色和黑色翻转
    inverted = cv2.bitwise_not(binary)

    # 锐化
    kernel = np.array([[0, -1, 0], [-1, 5, -1], [0, -1, 0]])
    sharpened = cv2.filter2D(binary, -1, kernel)

    # 调整对比度和亮度
    alpha = 1.5  # 对比度
    beta = 30    # 亮度
    adjusted = cv2.convertScaleAbs(sharpened, alpha=alpha, beta=beta)

    return adjusted

def batch_preprocess_images(input_folder, output_folder):
    """批量预处理图片函数"""
    # 遍历文件夹下的所有图片文件
    image_extensions = ['.jpg', '.jpeg', '.png']
    for root, dirs, files in os.walk(input_folder):
        for file in files:
            if any(file.lower().endswith(ext) for ext in image_extensions):
                file_path = os.path.join(root, file)
                # 读取图片
                image = cv2.imread(file_path)
                # 预处理图片
                processed_image = preprocess_image(image)
                # 按图片名称在out_folder中创建子文件夹
                sub_folder = os.path.splitext(file)[0]
                # 获取子文件夹的路径，相对于输出文件夹的路径
                output_subfolder = os.path.join(output_folder, sub_folder)
                if not os.path.exists(output_subfolder):
                    os.makedirs(output_subfolder)
                # 保存预处理后的图片到子文件夹
                output_file_path = os.path.join(output_subfolder, file)
                cv2.imwrite(output_file_path, processed_image)
    print("批量预处理完成。")

# ====================== 获取图片文件 ======================
@func_timer
def folder_epochs_images(input_folder, limit=10, skip=0, stop=1) -> list:
    """
    获取文件夹中的图片文件，根据limit、skip、stop参数进行分批

    参数:
    - input_folder: 输入文件夹路径
    - limit: 控制一次性输入数量，默认为10
    - skip: 控制从第skip批开始，默认为0表示从第一批开始
    - stop: 控制终止于第几批，默认为1

    返回:
    - 图片文件列表，每个元素为(file_path, file_name, subfolder_path, dir_name)
    """
    # 优先处理错误和边界情况
    if not os.path.exists(input_folder):
        print(f"输入文件夹不存在: {input_folder}")
        return []

    image_files = []
    image_extensions = ['.jpg', '.jpeg', '.png']
    batch_count = 0       # 当前批次号（从0开始计数）
    current_batch = []    # 当前批次收集的图片
    collected = False     # 是否已达到停止条件

    # 遍历所有子文件夹（深度优先遍历）
    for root, dirs, files in os.walk(input_folder):
        dirs.sort()  # 确保子目录按固定顺序遍历
        files.sort()  # 确保文件按固定顺序处理

        for file in files:
            # 检查文件扩展名
            if not any(file.lower().endswith(ext) for ext in image_extensions):
                continue

            # 构建完整路径信息
            file_path = os.path.join(root, file)
            dir_name = os.path.basename(root)
            subfolder = root

            # 将文件加入当前批次
            current_batch.append((file_path, file, subfolder, dir_name))

            # 检查是否达到批次大小
            if len(current_batch) != limit:
                continue

            # 判断是否在目标批次范围内
            if skip <= batch_count < stop:
                image_files.extend(current_batch)
            
            # 更新批次计数器并重置当前批次
            batch_count += 1
            current_batch = []

            # 如果达到停止条件，提前退出
            if batch_count >= stop:
                collected = True
                break

        # 如果已收集足够批次，立即终止遍历
        if collected:
            break

    # 处理最后一批未满的图片
    if not collected and current_batch and (skip <= batch_count < stop):
        image_files.extend(current_batch)

    return image_files

# ====================== 同步模式测试 ======================
@func_timer
def batch_ocr_images_sync(ocr: WeChatSyncOCR, image_files, limit=10, skip=0, stop=1):
    """
    使用同步模式批量处理OCR

    参数:
    - input_folder: 输入文件夹路径
    - output_folder: 输出文件夹路径
    - limit: 控制一次性输入数量，默认为10
    - skip: 控制从第skip批开始，默认为0表示从第一批开始
    - stop: 控制终止于第几批，默认为1
    """
    print(f"===== 同步模式测试 (limit={limit}, skip={skip}, stop={stop}) =====")

    try:
        total_processed = 0

        # 处理所有文件
        for file_path, file_name, subfolder_path, dir_name in image_files:

            # 使用func_timer装饰ocr.recognize函数
            recognize = func_timer(ocr.recognize)

            # 使用同步OCR进行识别
            ocr_result = recognize(file_path)

            # 获取文本结果
            text_list = ocr.get_texts(ocr_result)

            # 保存结果
            if not text_list:
                continue

            # 为每个图片创建单独的结果文件
            output_file_path = os.path.join(subfolder_path, f"{os.path.splitext(file_name)[0]}_sync.txt")
            with open(output_file_path, "w", encoding="utf-8") as f:
                f.write("\n".join(text_list))

            total_processed += 1
    except Exception as e:
        print(f"[同步模式] 出错: {e}")
        # 关闭OCR引擎
        ocr.close()

# ====================== 任务模式测试 ======================
@func_timer
def batch_ocr_images_task(ocr: WeChatTasksOCR, image_files, limit=10, skip=0, stop=1):
    """
    使用任务模式批量处理OCR

    参数:
    - input_folder: 输入文件夹路径
    - output_folder: 输出文件夹路径
    - limit: 控制一次性输入数量，默认为10
    - skip: 控制从第skip批开始，默认为0表示从第一批开始
    - stop: 控制终止于第几批，默认为1
    """
    print(f"===== 任务模式测试 (limit={limit}, skip={skip}, stop={stop}) =====")

    try:
        total_processed = 0

        # 提交所有任务
        tasks = []
        for file_path, file_name, subfolder_path, dir_name in image_files:
            task_id = ocr.submit_task(file_path)
            tasks.append((task_id, file_name, subfolder_path, dir_name))
            total_processed += 1

        # 获取所有任务结果
        for task_id, file_name, subfolder_path, dir_name in tasks:
            ocr_result = func_timer(ocr.get_result)(task_id)

            # 获取文本结果
            text_list = ocr.get_texts(ocr_result)

            # 保存结果
            if not text_list:
                continue

            # 为每个图片创建单独的结果文件
            output_file_path = os.path.join(subfolder_path, f"{os.path.splitext(file_name)[0]}_task.txt")
            with open(output_file_path, "w", encoding="utf-8") as f:
                f.write("\n".join(text_list))
    except Exception as e:
        print(f"[任务模式] 出错: {e}")
        # 关闭OCR引擎
        ocr.close()

def func_async_timer(func):
    async def wrapper(*args, **kwargs):
        start_time = time.time()
        result = await func(*args, **kwargs)
        end_time = time.time()
        print(f"{func.__name__} 耗时: {end_time - start_time:.2f}秒")
        return result
    return wrapper

# ====================== 异步模式测试 ======================
@func_async_timer
async def batch_ocr_images_async(ocr: WeChatAsyncOCR, image_files, limit=10, skip=0, stop=1):
    """
    使用异步模式批量处理OCR

    参数:
    - input_folder: 输入文件夹路径
    - output_folder: 输出文件夹路径
    - limit: 控制一次性输入数量，默认为10
    - skip: 控制从第skip批开始，默认为0表示从第一批开始
    - stop: 控制终止于第几批，默认为1
    """
    print(f"===== 异步模式测试 (limit={limit}, skip={skip}, stop={stop}) =====")

    try:
        total_processed = 0

        # 创建所有异步任务
        async_tasks = []
        for file_path, file_name, subfolder_path, dir_name in image_files:
            task = ocr.recognize(file_path)
            async_tasks.append((task, file_name, subfolder_path, dir_name))
            total_processed += 1

        # 并发执行所有任务
        for task, file_name, subfolder_path, dir_name in async_tasks:
            result = await task

            # 获取文本结果
            text_list = ocr.get_texts(result)

            # 保存结果
            if not text_list:
                continue

            # 为每个图片创建单独的结果文件
            output_file_path = os.path.join(subfolder_path, f"{os.path.splitext(file_name)[0]}_async.txt")
            async with aiofiles.open(output_file_path, "w", encoding="utf-8") as f:
                await f.write("\n".join(text_list))
    except Exception as e:
        print(f"[异步模式] 出错: {e}")
        # 关闭OCR引擎
        ocr.close()

# 定义主函数，传入输入文件夹路径和输出文件夹路径，以及控制参数
@func_timer
def main(engine, input_folder, output_folder, mode="all", limit=10, skip=0, stop=1):
    """
    主函数

    参数:
    - input_folder: 输入文件夹路径
    - output_folder: 输出文件夹路径
    - mode: 测试模式，可选值为"sync"(同步)、"task"(任务)、"async"(异步)、"all"(所有)
    - limit: 控制一次性输入数量，默认为10
    - skip: 控制从第skip批开始，默认为0表示从第一批开始
    - stop: 控制终止于第几批，默认为1
    """
    # 批量预处理图片
    # batch_preprocess_images(input_folder, output_folder)

    # 使用folder_epochs_images获取已经处理过limit、skip、stop参数的图片文件列表
    image_files = folder_epochs_images(input_folder, limit, skip, stop)

    # 优先处理错误和边界情况
    if not image_files:
        print("[任务模式] 未找到任何图片，处理结束")
        return

    # 根据模式选择测试方法
    if mode == "sync" or mode == "all":
        # 1. 同步模式
        batch_ocr_images_sync(engine.sync_ocr, image_files, limit, skip, stop)

    if mode == "tasks" or mode == "all":
        # 2. 任务模式
        batch_ocr_images_task(engine.tasks_ocr, image_files, limit, skip, stop)

    if mode == "async" or mode == "all":
        # 3. 异步模式
        asyncio.run(batch_ocr_images_async(engine.async_ocr, image_files, limit, skip, stop))

    print("\n===== 所有测试完成 =====")
    print("请查看输出文件夹中的结果文件。")

if __name__ == "__main__":
    # 输入文件夹路径
    input_folder = r"trains\data\xy2\images"
    # 输出文件夹路径
    output_folder = r"temp\xy2\ocr_result"

    # 示例：一次处理10张图片，从第0批开始，到第1批结束
    # 相当于只处理前10张图片
    limit = 10
    skip = 0
    stop = 34

    # 改为顺序执行测试模式
    modes = [
        "sync",
        "tasks",
        "async",
    ]

    try:

        ocr_engine = engine()
        main(ocr_engine, input_folder, output_folder, mode='tasks', limit=limit, skip=skip, stop=stop)
    except KeyboardInterrupt:
        print("\n[退出] 用户中断")
    except Exception as e:
        print(f"[退出] 出错: {e}")
