import time
import win32api
import win32con
from typing import Generator

from assist import AssistBasicToolkit

from event_bus import event_bus

from ..state import State
from .character import Character

class Characters:

    def __init__(self):
        self.hwnd = 0
        self.assist: AssistBasicToolkit = None

        self.characters = {}
        self.current_index = 0  # 当前索引

        self.child_cls_name = ''

        event_bus.subscribe('on_executor_started', self._on_executor_started)
        event_bus.subscribe('on_executor_stoped', self._on_executor_stoped)
        event_bus.subscribe('on_tasker_registered', self._on_tasker_registered)

    @classmethod
    def created(cls, hwnd, child_cls_name, assist: AssistBasicToolkit) -> 'Characters':
        self = cls()
        self.hwnd = hwnd
        self.assist = assist
        self.child_cls_name = child_cls_name
        return self

    def _on_executor_started(self):
        '''开始事件回调'''
        child_hwnds = self.get_child_characters()
        for child, cls_name, win_title in child_hwnds:
            self.bind_character(child, cls_name, win_title)

    def _on_executor_stoped(self):
        '''结束事件回调'''
        self.characters.clear()

    def _on_tasker_registered(self, task_names):
        '''任务注册回调'''
        
        # 这个方法后续可能会扩展成为给每一个窗口分配任务
        for hwnd, character in self.for_loop_characters():
            character.add_taskers(task_names)

    def get_child_characters(self):
        '''
        获取角色子窗口
        '''
        if not self.hwnd:  # 如果没有主窗口句柄，返回空列表
            return []
        
        # print(f'[Characters] 获取子窗口，主窗口句柄: {self.hwnd}, 子窗口类名: {self.child_cls_name}')

        child_hwnds = self.assist.window_ops.enum_child_windows(
            self.hwnd, cls_name=self.child_cls_name, require_visible=False
        )
        # print(f'[Characters] 获取子窗口数量: {len(child_hwnds)}')
        return child_hwnds

    def bind_character(self, hwnd, cls_name, win_title):
        '''
        绑定角色
        '''
        if hwnd in self.characters:
            return
        self.characters[hwnd] = Character.bind_character(hwnd, cls_name, win_title)

    def next_character(self) -> Character:
        '''
        切换到下一个角色
        '''
        if not self.characters:
            return

        # print(f'[Characters] 切换到下一个角色，当前索引: {self.current_index}, 角色数量: {len(self.characters)}')
        for _ in range(len(self.characters)):
            character = self._next_character()
            if not character:
                continue
            return character

    def _next_character(self) -> Character:
        '''获取当前索引的角色并递增索引'''
        hwnd = list(self.characters.keys())[self.current_index]
        self.current_index = (self.current_index + 1) % len(self.characters)
        return self.get_character(hwnd)

    def get_character(self, hwnd) -> Character:
        '''
        获取角色
        '''
        if hwnd not in self.characters:
            return 
        character = self._get_character(hwnd)
        if not State.is_available(character.status):
            return
        return character

    def _get_character(self, hwnd) -> Character:
        '''
        获取角色
        '''
        return self.characters[hwnd]

    def for_loop_characters(self) -> Generator[int, Character, None]:
        '''
        循环角色
        '''
        for hwnd, character in self.characters.items():
            yield hwnd, character

    def switch_to_window(self, character: Character) -> bool:
        """切换到窗口"""

        '''
        窗口切换按键：Ctrl + Tab
        切换窗口时校验窗口是否切换成功，如校验hwnd是否一致或者校验self.name是否在窗口标题中
        流程：
        1. 按Ctrl + Tab
        2. 获取当前窗口信息
        3. 校验窗口信息
        4. 窗口信息一致则切换成功，不一致则切换失败
        5. 切换窗口失败时继续从1-4循环
        6. 切换窗口成功时返回
        '''
        if self._is_character_window(character):
            return True

        # 最大重试次数
        MAX_RETRY = 5
        # 切换延迟(秒)
        SWITCH_DELAY = 0.5

        for _ in range(MAX_RETRY):
            # 发送Ctrl+Tab组合键
            win32api.keybd_event(win32con.VK_CONTROL, 0, 0, 0)
            win32api.keybd_event(win32con.VK_TAB, 0, 0, 0)
            win32api.keybd_event(win32con.VK_TAB, 0, win32con.KEYEVENTF_KEYUP, 0)
            win32api.keybd_event(win32con.VK_CONTROL, 0, win32con.KEYEVENTF_KEYUP, 0)

            # 等待窗口切换
            time.sleep(SWITCH_DELAY)

            # 校验窗口信息
            if not self._is_character_window(character):
                continue

            # 切换成功
            # print(f"[Characters] switch to window success hwnd in [{character.hwnd}] name is [{character.name or None}]")
            return True

        # 切换失败
        print(f"[Characters] switch to window failed hwnd in [{character.hwnd}] name is [{character.name or None}]")
        return False

    def _is_character_window(self, character: Character) -> bool:
        '''
        获取窗口信息
        '''
        title = self.assist.window_ops.get_window_title(self.hwnd)

        # 查找子窗口
        child, cls_name, title = self.assist.window_ops.find_child_window(
            self.hwnd, cls_name=self.child_cls_name, require_visible=True
        )

        # print(f"[Characters] get window is [{child}] title is [{title or None}] source hwnd is [{self.hwnd}] source name is [{character.name or None}]")

        if child == character.hwnd:
            return True
        elif character.name and title.startswith(character.name):
            return True
        else:
            return False
        
    def is_all_finished(self) -> bool:
        """
        检查所有角色是否完成任务
        """
        all_finished = []
        for _, character in self.for_loop_characters():
            if character and character.is_all_finished():
                all_finished.append(True)
        return len(all_finished) == len(self.characters) and all(all_finished)