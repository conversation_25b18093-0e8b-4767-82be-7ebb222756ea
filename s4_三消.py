import os
import time
import difflib
import random
from PIL import Image, ImageDraw
import pyautogui
import threading
import keyboard
import traceback
import numpy as np
from collections import defaultdict

from assist import AssistBasicToolkit
from event_bus import event_bus

class TripleEliminationGameAssistant:
    def __init__(self):
        self.hwnd = 0
        
        # assist工具包
        self.assist: AssistBasicToolkit = None
        
        # assist工具包的操作对象
        self.window_ops = None
        self.screenshot = None
        self.detection = None
        self.image_search = None
        self.hook_thread = None
        self.is_running = False
        self.is_waiting = False
        
        # 游戏结束图片
        self.game_fail_image = Image.open(r'models\sanxiao\放弃复活.png')
        self.game_fail_confirm_image = Image.open(r'models\sanxiao\放弃复活确定.png')
        self.game_success_image = Image.open(r'models\sanxiao\点击关闭屏幕.png')
        self.close_blank_area_image = Image.open(r'models\sanxiao\点击空白区域关闭.png')
        self.start_game_image = Image.open(r'models\sanxiao\前往绘画.png')
        
        # 游戏区域坐标（需要根据实际情况调整）
        self.main_area = (285, 100, 1230, 625)  # 主游戏区域
        self.storage_area = (405, 750, 1185, 895)  # 预存区域
        
        self.reverse_count = 0
        # 订阅程序开始事件与结束事件
        event_bus.subscribe('on_executor_started', self._on_executor_started)
        event_bus.subscribe('on_executor_stoped', self._on_executor_stoped)

    @classmethod
    def created(cls, hwnd, assist: AssistBasicToolkit) -> 'TripleEliminationGameAssistant':
        self = cls()
        self.hwnd = hwnd
        self.assist = assist
        self.register_assist_objects()
        return self

    def register_assist_objects(self):
        self.window_ops = self.assist.window_ops
        self.screenshot = self.assist.screenshot
        self.detection = self.assist.detection
        self.image_search = self.assist.image_search

    def _on_executor_started(self):
        '''开始事件回调'''
        if self.is_running:
            return

        self.is_running = True

        self.hook_thread = threading.Thread(target=self.keyboard_listener)
        self.hook_thread.daemon = True
        self.hook_thread.start()

        self.run()

    def _on_executor_stoped(self):
        '''结束事件回调'''
        if not self.is_running:
            return
        
        self.is_running = False
        # 释放资源
        self.assist.close()

    def keyboard_listener(self):
        """监听键盘输入"""
        while self.is_running:
            if keyboard.is_pressed('enter'):
                print(f"按键 enter")
            elif keyboard.is_pressed('esc'):
                print(f"按键 esc")
                self.is_running = False
            elif keyboard.is_pressed('space'):
                print(f"按键 space")
                self.is_waiting = False
            time.sleep(0.05)

    def _check_image_state(self, current_img, image, state_name):
        """通用图像状态检测方法"""
        result = self.image_search.find_image(current_img, image)
        if result:
            return state_name, result
        return None, None

    def detect_current_game_state(self, current_img):
        """检测当前游戏状态"""
        # 定义状态检测配置 - 按优先级排序，确认按钮优先级最高
        state_configs = [
            (self.game_fail_confirm_image, "game_fail_confirm"),  # 优先检测确认按钮
            (self.close_blank_area_image, "close_blank_area"),    # 然后检测关闭按钮
            (self.game_success_image, "game_success"),            # 游戏成功
            (self.start_game_image, "start_screen"),              # 开始界面
            (self.game_fail_image, "game_failed"),                # 最后检测失败按钮
        ]

        # 按优先级检测各种状态
        for image, state_name in state_configs:
            state, result = self._check_image_state(current_img, image, state_name)
            if state:
                return state, result

        # 检查是否在游戏界面（有方块可以检测）
        if self.is_game_ready_for_play():
            return "in_game", None

        return "unknown", None

    def _handle_state_action(self, image, button_name, max_attempts=5):
        """通用状态处理方法 - 不使用硬编码等待时间"""
        print(f"处理{button_name}状态：点击{button_name}")
        success = self._click_button_with_retry(image, button_name, max_attempts=max_attempts)
        if success:
            print(f"成功点击{button_name}按钮")
        else:
            print(f"点击{button_name}按钮失败")
        return success

    def handle_game_state(self, state):
        """统一的游戏状态处理方法 - 移除硬编码等待时间"""
        state_configs = {
            "game_failed": (self.game_fail_image, "放弃复活", 3),
            "game_fail_confirm": (self.game_fail_confirm_image, "放弃复活确定", 5),
            "game_success": (self.game_success_image, "点击关闭屏幕", 3),
            "close_blank_area": (self.close_blank_area_image, "点击空白区域关闭", 5),
            "start_screen": (self.start_game_image, "前往绘画", 5),
        }

        if state in state_configs:
            image, button_name, max_attempts = state_configs[state]
            return self._handle_state_action(image, button_name, max_attempts)

        return False

    def check_and_handle_game_state(self, current_img):
        """检查并处理游戏状态变化 - 使用主动检测"""
        state, result = self.detect_current_game_state(current_img)

        if state == "in_game":
            # 游戏进行中，可以继续检测方块
            return False

        if state == "game_failed":
            print(f"检测到游戏失败，{result}")
            if self.handle_game_state(state):
                # 主动等待下一个状态
                next_state, _ = self.wait_for_state_change(["game_fail_confirm", "close_blank_area", "start_screen"], max_wait_seconds=3)
                if next_state:
                    print(f"状态已切换到: {next_state}")
            return True

        elif state in ["game_fail_confirm", "game_success", "close_blank_area", "start_screen"]:
            print(f"检测到{state}状态，{result}")
            success = self.handle_game_state(state)
            if success:
                if state == "start_screen":
                    # 等待进入游戏
                    next_state, _ = self.wait_for_state_change(["in_game"], max_wait_seconds=6)
                    if next_state == "in_game":
                        print("成功进入游戏界面")
                        return False  # 可以继续游戏
                else:
                    # 等待下一个状态
                    expected_states = {
                        "game_fail_confirm": ["close_blank_area", "start_screen"],
                        "game_success": ["start_screen"],
                        "close_blank_area": ["start_screen"]
                    }
                    if state in expected_states:
                        next_state, _ = self.wait_for_state_change(expected_states[state], max_wait_seconds=3)
                        if next_state:
                            print(f"状态已切换到: {next_state}")
            return True

        # 未知状态，短暂等待
        print(f"未知游戏状态: {state}")
        time.sleep(0.5)
        return True

    def wait_for_state_change(self, expected_states, max_wait_seconds=10, check_interval=0.2):
        """等待状态变化到期望的状态之一"""
        start_time = time.time()
        while time.time() - start_time < max_wait_seconds:
            try:
                current_img = self.screenshot.capture_client_area()
                if current_img is None:
                    time.sleep(check_interval)
                    continue

                state, result = self.detect_current_game_state(current_img)
                if state in expected_states:
                    print(f"检测到期望状态: {state}")
                    return state, result

                time.sleep(check_interval)
            except Exception as e:
                print(f"等待状态变化时出错: {e}")
                time.sleep(check_interval)

        print(f"等待状态变化超时，期望状态: {expected_states}")
        return None, None

    def ensure_game_ready(self):
        """确保游戏准备就绪，可以开始检测方块 - 使用主动检测而非硬编码等待"""
        max_attempts = 15  # 增加尝试次数
        for attempt in range(max_attempts):
            try:
                current_img = self.screenshot.capture_client_area()
                if current_img is None:
                    print(f"截图失败，尝试 {attempt + 1}/{max_attempts}")
                    time.sleep(0.5)
                    continue

                state, _ = self.detect_current_game_state(current_img)

                if state == "in_game":
                    print("游戏已准备就绪，可以开始检测方块")
                    return True

                elif state == "game_failed":
                    print("检测到游戏失败，开始处理...")
                    if self.handle_game_state(state):
                        # 点击放弃复活后，等待确认按钮出现
                        next_state, _ = self.wait_for_state_change(["game_fail_confirm", "close_blank_area", "start_screen"], max_wait_seconds=3)
                        if next_state:
                            continue  # 继续处理下一个状态

                elif state == "game_fail_confirm":
                    print("检测到放弃复活确定，处理中...")
                    if self.handle_game_state(state):
                        # 点击确定后，等待关闭按钮或开始界面出现
                        next_state, _ = self.wait_for_state_change(["close_blank_area", "start_screen"], max_wait_seconds=3)
                        if next_state:
                            continue

                elif state == "close_blank_area":
                    print("检测到点击空白区域关闭，处理中...")
                    if self.handle_game_state(state):
                        # 点击关闭后，等待开始界面出现
                        next_state, _ = self.wait_for_state_change(["start_screen"], max_wait_seconds=3)
                        if next_state:
                            continue

                elif state == "start_screen":
                    print("检测到开始界面，点击前往绘画...")
                    if self.handle_game_state(state):
                        # 点击前往绘画后，等待游戏界面出现
                        next_state, _ = self.wait_for_state_change(["in_game"], max_wait_seconds=6)
                        if next_state == "in_game":
                            print("成功进入游戏界面")
                            return True

                elif state == "game_success":
                    print("检测到游戏成功，点击关闭...")
                    if self.handle_game_state(state):
                        # 点击关闭后，等待开始界面出现
                        next_state, _ = self.wait_for_state_change(["start_screen"], max_wait_seconds=3)
                        if next_state:
                            continue

                else:
                    print(f"未知状态: {state}，等待...")
                    time.sleep(0.5)

            except Exception as e:
                print(f"确保游戏准备就绪时出错，尝试 {attempt + 1}/{max_attempts}: {e}")
                time.sleep(0.5)

        print("无法确保游戏准备就绪，已达到最大尝试次数")
        return False

    def _click_button_with_retry(self, target_image, button_name, max_attempts=5):
        """带重试的按钮点击方法"""
        for attempt in range(max_attempts):
            try:
                # 重新截图
                current_img = self.screenshot.capture_client_area()
                if current_img is None:
                    print(f"截图失败，{button_name} 尝试 {attempt + 1}/{max_attempts}")
                    time.sleep(0.5)
                    continue

                # 查找目标按钮
                button_result = self.image_search.find_image(current_img, target_image)
                if button_result:
                    print(f"找到{button_name}按钮，点击，尝试 {attempt + 1}/{max_attempts}")
                    _, (x1, y1), (x2, y2) = button_result
                    x1, y1, x2, y2 = map(int, [x1, y1, x2, y2])
                    # 点击按钮中心位置
                    center_x = (x1 + x2) // 2
                    center_y = (y1 + y2) // 2
                    client_x, client_y = self.window_ops.client_to_screen(self.hwnd, (center_x, center_y))

                    # 只点击一次，避免关闭弹出的对话框
                    pyautogui.click(client_x, client_y)

                    time.sleep(0.5)  # 等待点击生效
                    return True
                else:
                    print(f"未找到{button_name}按钮，尝试 {attempt + 1}/{max_attempts}")
                    time.sleep(0.5)

            except Exception as e:
                print(f"点击{button_name}按钮时出错，尝试 {attempt + 1}/{max_attempts}: {e}")
                time.sleep(0.5)

        print(f"点击{button_name}按钮失败，已达到最大尝试次数")
        return False

    def is_game_ready_for_play(self):
        """检查游戏是否准备好进行游戏"""
        try:
            current_img = self.screenshot.capture_client_area()
            if current_img is None:
                return False

            # 检查主区域是否有方块（说明游戏已经开始）
            main_img = current_img.crop(self.main_area)
            main_results = self.detection.detect_objects(main_img)

            # 如果检测到方块，说明游戏已经准备好
            if len(main_results) > 5:  # 至少要有5个方块才认为游戏准备好
                return True

            return False

        except Exception as e:
            print(f"检查游戏准备状态时出错: {e}")
            return False

    def get_similarity(self, str1, str2):
        """计算两个文本的相似度"""
        return difflib.SequenceMatcher(None, str1, str2).ratio()

    def update_sequence_blocks(self, sequence, blocks):
        """更新方块序列"""
        sequence.extend(blocks)

    def analyze_blocks(self, results):
        """分析主区域和预存区域的方块分布"""
        # 分析主区域方块
        _blocks = defaultdict(list)
        for box in results.boxes:
            conf = box.conf.item()
            if conf < 0.25:
                continue
            cls_id = int(box.cls.item())
            name = results.names[cls_id]
            x1, y1, x2, y2 = map(int, box.xyxy[0].cpu().numpy())

            # 计算相对于裁剪区域的中心点，然后加上区域偏移
            cx = (x1 + x2) / 2
            cy = (y1 + y2) / 2
            # 转换为客户区坐标
            ccx = cx + self.main_area[0]
            ccy = cy + self.main_area[1]

            _blocks[cls_id].append({
                'name': name,  # 方块名称
                'class_id': cls_id,
                'position': (ccx, ccy),
                'location': (x1, y1, x2, y2),  # 方块位置
            })

            # x1 = self.main_area[0] + x1
            # y1 = self.main_area[1] + y1
            # x2 = self.main_area[0] + x2
            # y2 = self.main_area[1] + y2

            # xywhn = box.xywhn[0].tolist()
            # data_line = f"{cls_id} {xywhn[0]:.6f} {xywhn[1]:.6f} {xywhn[2]:.6f} {xywhn[3]:.6f}"
            # print(f"检测到{cls_id}方块：{name}，位置：{ccx},{ccy}，{x1},{y1},{x2},{y2},{data_line}")

        return _blocks

    def draw_area_distribution(self, image: Image.Image, area_blocks, min_x, min_y, area_width, area_height, n_splits):
        """
        绘制区域划分的可视化图
        Args:
            image: 原始图片
            area_blocks: 区域分布信息
            min_x, min_y: 区域最小坐标
            area_width, area_height: 每个区域的宽高
            n_splits: 区域划分数量
        """
        draw, debug_img = self.created_draw(image)
        
        # 绘制区域网格
        for i in range(n_splits + 1):
            # 绘制垂直线
            x = min_x + i * area_width
            draw.line([(x, min_y), (x, min_y + n_splits * area_height)], fill="blue", width=2)
            
            # 绘制水平线
            y = min_y + i * area_height
            draw.line([(min_x, y), (min_x + n_splits * area_width, y)], fill="blue", width=2)
        
        # 在每个区域中心显示方块数量
        for area_id, blocks in area_blocks.items():
            area_y = area_id // n_splits
            area_x = area_id % n_splits
            
            center_x = min_x + (area_x + 0.5) * area_width
            center_y = min_y + (area_y + 0.5) * area_height
            
            # 绘制方块数量
            text = str(len(blocks))
            draw.text((center_x, center_y), text, fill="red")
        
        # 保存debug图片
        self.output_draw(debug_img)

    def analyze_area_distribution(self, main_blocks, n_splits=3):
        """
        分析主区域方块分布
        Args:
            main_blocks: 主区域方块信息
            n_splits: 区域划分数量
        Returns:
            dict: {区域id: [方块列表]}
        """
        # 1. 获取所有方块的位置信息
        all_blocks = []
        for blocks in main_blocks.values():
            all_blocks.extend(blocks)
        
        if not all_blocks:
            return {}
            
        # 2. 计算所有方块的位置范围
        min_x = float('inf')
        max_x = float('-inf')
        min_y = float('inf')
        max_y = float('-inf')
        
        for block in all_blocks:
            x1, y1, x2, y2 = block['location']
            min_x = min(min_x, x1)
            max_x = max(max_x, x2)
            min_y = min(min_y, y1)
            max_y = max(max_y, y2)
        
        # 3. 计算每个区域的大小
        width = max_x - min_x
        height = max_y - min_y
        area_width = width / n_splits
        area_height = height / n_splits
        
        # 4. 初始化区域字典
        area_blocks = defaultdict(list)
        
        # 5. 将方块分配到对应区域
        for block in all_blocks:
            x1, y1, x2, y2 = block['location']
            center_x = (x1 + x2) / 2 - min_x
            center_y = (y1 + y2) / 2 - min_y
            
            # 计算方块所在区域
            area_x = int(center_x / area_width)
            area_y = int(center_y / area_height)
            
            # 确保区域索引在有效范围内
            area_x = min(max(area_x, 0), n_splits - 1)
            area_y = min(max(area_y, 0), n_splits - 1)
            
            # 计算区域ID（从左到右，从上到下编号）
            area_id = area_y * n_splits + area_x
            area_blocks[area_id].append(block)
        
        return area_blocks, (min_x, min_y, area_width, area_height)

    def select_blocks_from_area(self, blocks, area_blocks, n_blocks, select_least=False):
        """
        从指定区域选择方块
        Args:
            blocks: 某个类型的所有方块
            area_blocks: 区域分布信息
            n_blocks: 需要选择的方块数量
            select_least: True表示从方块最少的区域选择，False表示从方块最多的区域选择
        Returns:
            list: 选中的方块列表
        """
        selected_blocks = []
        
        # 统计每个区域中目标类型方块的数量
        area_type_counts = {}
        for area_id, area_block_list in area_blocks.items():
            # 找到当前区域中属于目标类型的方块
            current_area_blocks = [
                block for block in blocks 
                if block in area_block_list
            ]
            if current_area_blocks:
                area_type_counts[area_id] = len(current_area_blocks)
        
        # 根据区域内方块数量排序
        sorted_areas = sorted(
            area_type_counts.items(),
            key=lambda x: x[1],
            reverse=not select_least  # select_least为True时按数量升序，False时按数量降序
        )
        
        # 从排序后的区域中选择方块
        for area_id, _ in sorted_areas:
            area_block_list = area_blocks[area_id]
            # 找到当前区域中属于目标类型的方块
            current_area_blocks = [
                block for block in blocks 
                if block in area_block_list
            ]
            
            if current_area_blocks:
                # 添加到选中列表
                selected_blocks.extend(current_area_blocks)
                
                # 如果已经选够了足够的方块，就返回
                if len(selected_blocks) >= n_blocks:
                    return selected_blocks[:n_blocks]
        
        return selected_blocks[:n_blocks] if selected_blocks else []

    def select_blocks_to_click(self, main_blocks, storage_blocks, area_blocks):
        """
        选择需要点击的方块序列
        Args:
            main_blocks: 主区域方块信息
            storage_blocks: 预存区方块信息
            area_blocks: 区域分布信息
        Returns:
            list: 选中的方块序列
        """
        total_storage = sum([len(b) for b in storage_blocks.values()])
        residue = 7 - total_storage  # 预存区剩余容量

        sequence = []
        self.reverse_count += 1
        
        main_blocks_sort = sorted(main_blocks.items(), key=lambda x: len(x[1]), reverse=True)
        storage_blocks_sort = sorted(storage_blocks.items(), key=lambda x: len(x[1]), reverse=True)

        # 剩余容量小于3，需要尽量保证能消除一个
        if residue < 3:
            for cls_id, blocks in storage_blocks_sort:
                storage_count = len(blocks)
                needed = 3 - storage_count
                if cls_id not in main_blocks: continue
                if needed > len(main_blocks[cls_id]): continue
                
                # 从方块数最少的区域中选择方块
                selected_blocks = self.select_blocks_from_area(
                    main_blocks[cls_id], 
                    area_blocks,
                    needed,
                    select_least=True  # 从方块最少的区域开始选择
                )
                if selected_blocks:
                    self.update_sequence_blocks(sequence, selected_blocks)
                    break
            if sequence: return sequence

        # 剩余容量大于3，可以从主区域中消除1组
        if residue > 3:
            # 第一种情况，能消除一组的
            for cls_id, _blocks in main_blocks_sort:
                if len(_blocks) >= 3:
                    # 从方块数最多的区域中选择方块
                    n_blocks = len(_blocks) if len(_blocks) % 3 == 0 else 3
                    selected_blocks = self.select_blocks_from_area(
                        _blocks,
                        area_blocks,
                        n_blocks,
                        select_least=False  # 从方块最多的区域开始选择
                    )
                    if selected_blocks:
                        print(f"策略1：主区域有{len(_blocks)}个{cls_id}方块，选择{len(selected_blocks)}个进行消除")
                        self.update_sequence_blocks(sequence, selected_blocks)
            if sequence: return sequence

            # 第二种情况，预存区能消除的
            for cls_id, blocks in storage_blocks_sort:
                storage_count = len(blocks)
                needed = 3 - storage_count
                if cls_id not in main_blocks: continue
                if needed > len(main_blocks[cls_id]): continue
                
                # 从方块数最多的区域中选择方块
                selected_blocks = self.select_blocks_from_area(
                    main_blocks[cls_id],
                    area_blocks,
                    needed,
                    select_least=True  # 从方块最少的区域开始选择
                )
                if selected_blocks:
                    self.update_sequence_blocks(sequence, selected_blocks)
                    break
            if sequence: return sequence

        # 策略3：卡壳处理 - 选择数量最多的方块加入预存区
        if main_blocks:
            # 随机从主区域中获取一个加入预存区
            # cls_id, _blocks = next(iter(main_blocks.items()))
            cls_id, _blocks = max(main_blocks.items(), key=lambda x: len(x[1]))
            # 从方块数最少的区域中选择一个方块
            selected_blocks = self.select_blocks_from_area(
                _blocks,
                area_blocks,
                1,
                select_least=True  # 从方块最少的区域开始选择
            )
            # area_id, selected_blocks = min(area_blocks.items(), key=lambda x: len(x[1]))
            if selected_blocks:
                self.update_sequence_blocks(sequence, _blocks)
                print(f"策略3：卡壳处理，从方块数最少的区域中获取一个方块加入预存区")

        return sequence
    
    def click_blocks(self, blocks):
        """点击一系列方块"""
        for block in blocks:
            self.click_block(block)

    def click_block(self, block):
        client_x, client_y = self.window_ops.client_to_screen(
            self.hwnd, (int(block['position'][0]), int(block['position'][1]))
        )
        pyautogui.click(client_x, client_y, duration=0.001)
        time.sleep(0.1)  # 防止操作过快

    def created_draw(self, image: Image.Image):
        debug_img = image.copy()
        draw = ImageDraw.Draw(debug_img)
        return draw, debug_img
    
    def output_draw(self, image: Image.Image, temp_dir='temp'):
        # 创建临时目录用于调试
        temp_dir = os.path.join(temp_dir, 'sanxiao')
        os.makedirs(temp_dir, exist_ok=True)
        image_name = f"{time.strftime('%Y%m%d%H%M%S', time.localtime(time.time()))}.jpg"
        image.save(os.path.join(temp_dir, image_name))

    def click_blocks_draw(self, image, blocks, temp_dir='temp'):
        """点击一系列方块，并在debug_img上绘制"""
        draw, debug_img = self.created_draw(image)
        for block in blocks:
            x1, y1, x2, y2 = block['location']
            x1 = self.main_area[0] + x1
            y1 = self.main_area[1] + y1
            x2 = self.main_area[0] + x2
            y2 = self.main_area[1] + y2
            draw.rectangle((x1, y1, x2, y2), outline="red", width=5)
        self.output_draw(debug_img, temp_dir=temp_dir)

    def _main_loop(self):
        '''程序操作'''
        # 客户区域截图
        current_img = self.screenshot.capture_client_area()
        if current_img is None:
            time.sleep(1)
            return

        # 检查并处理游戏状态变化
        if self.check_and_handle_game_state(current_img):
            return

        main_img = current_img.crop(self.main_area)
        storage_img = current_img.crop(self.storage_area)
        
        # 检测主区域和预存区域目标
        main_results = self.detection.detect_objects(main_img)
        storage_results = self.detection.detect_objects(storage_img)
        
        # 分析两个区域的方块分布
        main_blocks = self.analyze_blocks(main_results)
        if not main_blocks:
            time.sleep(1)
            return

        storage_blocks = self.analyze_blocks(storage_results)
        
        # 获取区域分布信息
        area_blocks, area_info = self.analyze_area_distribution(main_blocks, n_splits=5)
        
        # 绘制区域划分图
        self.draw_area_distribution(main_img, area_blocks, *area_info, n_splits=5)
        
        # 选择要点击的方块序列
        blocks_to_click = self.select_blocks_to_click(main_blocks, storage_blocks, area_blocks)

        if not blocks_to_click:
            time.sleep(1)
            return
        
        # 执行点击
        print(f"本次将点击 {len(blocks_to_click)} 个方块")
        self.click_blocks(blocks_to_click)

        # 绘制debug图片
        # self.click_blocks_draw(current_img, blocks_to_click)

        for index, image_results in enumerate(zip([storage_img, main_img], [storage_results, main_results]), 1):
            image, results = image_results
            images_dir = r"D:\Soft\Yolo\datasets\sanxiao\images"
            os.makedirs(images_dir, exist_ok=True)
            timestamp = time.strftime('%Y%m%d%H%M%S', time.localtime(time.time()))
            image_name = timestamp.zfill(13) + f"{index}.jpg"
            image_path = os.path.join(images_dir, image_name)
            image.save(image_path)
            self.write_predict_labels(results, image_path)

    def process_results(self, results, conf_threshold=0.25):
        """处理YOLOv8结果并返回原始标注格式"""
        output = []

        for box in results.boxes:
            conf = box.conf.item()
            if conf < conf_threshold:
                continue
                
            cls_id = int(box.cls.item())
            xywhn = box.xywhn[0].tolist()

            data_line = f"{cls_id} {xywhn[0]:.6f} {xywhn[1]:.6f} {xywhn[2]:.6f} {xywhn[3]:.6f}"
            output.append(data_line)

        return output

    def write_predict_labels(self, results, image_path):
        '''
        r"trains\data\sanxiao\tests\images\20250623130958.jpg"
        '''
        annotations = self.process_results(results)
        base_dir = os.path.dirname(os.path.dirname(image_path))
        labels_path = os.path.join(base_dir, 'labels')
        os.makedirs(labels_path, exist_ok=True)
        
        image_name = os.path.basename(image_path)
        label_path = os.path.join(labels_path, image_name.replace('.jpg', '.txt'))

        with open(label_path, 'a') as f:
            for line in annotations:
                f.write(line + '\n')

    def wait_keyboard_event(self):
        """等待键盘事件"""
        while self.is_waiting and self.is_running:
            time.sleep(1)

    def run(self):
        """程序主循环"""
        try:
            print("三消游戏辅助脚本启动...")

            # 首先确保游戏准备就绪
            if not self.ensure_game_ready():
                print("游戏初始化失败，程序退出")
                self.is_running = False
                return

            while self.is_running:
                self.is_waiting = True

                self._main_loop()

                # 等待按键"空格"继续下一轮循环
                # self.wait_keyboard_event()
                time.sleep(0.3)

        except KeyboardInterrupt:
            print("程序已停止，用户主动操作")
            event_bus.publish('_on_executor_stoped')
        except Exception as e:
            print(traceback.format_exc())

if __name__ == "__main__":
    assist = AssistBasicToolkit()
    # 注册并绑定窗口操作对象
    hwnd = assist.register_and_bind_window_objects('最强祖师')
    # 记录模型路径
    assist.detection.load_model(r"models\sanxiao\best.pt")

    assistant = TripleEliminationGameAssistant.created(hwnd, assist)

    # 发布开始事件
    event_bus.publish('on_executor_started')


