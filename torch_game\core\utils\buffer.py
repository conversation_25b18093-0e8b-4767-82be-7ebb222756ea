"""
经验回放缓冲区
用于存储和采样强化学习训练数据
"""

import numpy as np
from typing import Dict, List, Tuple, Any
import random

class ReplayBuffer:
    """经验回放缓冲区"""
    
    def __init__(self, capacity: int):
        """
        初始化经验回放缓冲区
        
        Args:
            capacity: 缓冲区容量
        """
        self.capacity = capacity
        self.buffer = []
        self.position = 0
        
    def push(self, transition: Dict[str, Any]):
        """
        将一个transition添加到缓冲区
        
        Args:
            transition: 包含单步转换的字典，通常包括state, action, reward, next_state, done等
        """
        if len(self.buffer) < self.capacity:
            self.buffer.append(None)
        self.buffer[self.position] = transition
        self.position = (self.position + 1) % self.capacity
        
    def sample(self, batch_size: int) -> Dict[str, np.ndarray]:
        """
        从缓冲区中随机采样一批数据
        
        Args:
            batch_size: 批次大小
            
        Returns:
            batch: 包含批次数据的字典，每个键对应一个numpy数组
        """
        batch = random.sample(self.buffer, batch_size)
        
        # 将批次中的每个字段堆叠成数组
        return {
            k: np.array([d[k] for d in batch]) 
            for k in batch[0].keys()
        }
        
    def __len__(self) -> int:
        """
        获取缓冲区中的样本数量
        
        Returns:
            length: 样本数量
        """
        return len(self.buffer)
        
    def clear(self):
        """清空缓冲区"""
        self.buffer = []
        self.position = 0


class PPOBuffer:
    """PPO算法专用缓冲区"""
    
    def __init__(self, state_dim: int, action_dim: int, capacity: int, gamma: float = 0.99, lam: float = 0.95):
        """
        初始化PPO缓冲区
        
        Args:
            state_dim: 状态维度
            action_dim: 动作维度
            capacity: 缓冲区容量
            gamma: 折扣因子
            lam: GAE lambda参数
        """
        self.state_dim = state_dim
        self.action_dim = action_dim
        self.capacity = capacity
        self.gamma = gamma
        self.lam = lam
        
        # 缓冲区数据
        self.states = np.zeros((capacity, state_dim), dtype=np.float32)
        self.actions = np.zeros(capacity, dtype=np.int32)
        self.rewards = np.zeros(capacity, dtype=np.float32)
        self.values = np.zeros(capacity, dtype=np.float32)
        self.log_probs = np.zeros(capacity, dtype=np.float32)
        self.dones = np.zeros(capacity, dtype=np.bool_)
        
        self.ptr = 0
        self.path_start_idx = 0
        self.max_size = capacity
        
    def store(self, state, action, reward, value, log_prob, done):
        """
        存储一个transition
        
        Args:
            state: 状态
            action: 动作
            reward: 奖励
            value: 价值估计
            log_prob: 动作对数概率
            done: 是否结束
        """
        assert self.ptr < self.max_size
        
        self.states[self.ptr] = state
        self.actions[self.ptr] = action
        self.rewards[self.ptr] = reward
        self.values[self.ptr] = value
        self.log_probs[self.ptr] = log_prob
        self.dones[self.ptr] = done
        
        self.ptr += 1
        
    def finish_path(self, last_value=0):
        """
        完成一条轨迹，计算优势函数和回报
        
        Args:
            last_value: 如果轨迹被截断，提供最后状态的价值估计
        """
        path_slice = slice(self.path_start_idx, self.ptr)
        rewards = np.append(self.rewards[path_slice], last_value)
        values = np.append(self.values[path_slice], last_value)
        
        # GAE-Lambda优势估计
        deltas = rewards[:-1] + self.gamma * values[1:] - values[:-1]
        advantages = np.zeros_like(self.rewards[path_slice])
        
        gae = 0
        for t in reversed(range(len(advantages))):
            gae = deltas[t] + self.gamma * self.lam * (1 - self.dones[self.path_start_idx + t]) * gae
            advantages[t] = gae
            
        # 计算回报
        returns = advantages + self.values[path_slice]
        
        self.advantages = advantages
        self.returns = returns
        self.path_start_idx = self.ptr
        
    def get(self):
        """
        获取所有存储的数据
        
        Returns:
            data: 包含所有数据的字典
        """
        assert self.ptr == self.max_size
        
        # 重置指针
        self.ptr, self.path_start_idx = 0, 0
        
        # 标准化优势
        adv_mean = np.mean(self.advantages)
        adv_std = np.std(self.advantages)
        self.advantages = (self.advantages - adv_mean) / (adv_std + 1e-8)
        
        return {
            'states': self.states,
            'actions': self.actions,
            'log_probs': self.log_probs,
            'advantages': self.advantages,
            'returns': self.returns
        }
        
    def clear(self):
        """清空缓冲区"""
        self.ptr, self.path_start_idx = 0, 0